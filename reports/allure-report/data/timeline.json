{"uid": "ab17fc5a4eb3bca4b216b548c7f9fcbc", "name": "timeline", "children": [{"name": "SHCYbucy-pc", "children": [{"name": "36044-MainThread", "children": [{"name": "测试check battery information返回正确的不支持响应", "uid": "b766f5a20f897743", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753761631982, "stop": 1753761641998, "duration": 10016}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to notifications and status bar settings返回正确的不支持响应", "uid": "c297d430a8b59844", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753765470565, "stop": 1753765483040, "duration": 12475}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试driving mode返回正确的不支持响应", "uid": "f8e5b04c258276eb", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753764915935, "stop": 1753764924701, "duration": 8766}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试check mobile data balance of sim2返回正确的不支持响应", "uid": "2a28697021ec7d75", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753761655231, "stop": 1753761664149, "duration": 8918}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to adaptive brightness settings返回正确的不支持响应", "uid": "26de3ee7bd066adc", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753765267520, "stop": 1753765280068, "duration": 12548}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable accelerate dialogue返回正确的不支持响应", "uid": "e3bf02bacdd2495b", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753761790803, "stop": 1753761799572, "duration": 8769}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试the second返回正确的不支持响应", "uid": "487f42ee1097884f", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753766564387, "stop": 1753766572770, "duration": 8383}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to equilibrium mode返回正确的不支持响应", "uid": "ce8424deaa12d0d3", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753766474322, "stop": 1753766483379, "duration": 9057}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试enable touch optimization返回正确的不支持响应", "uid": "144fa92bac6d7d3c", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753765125128, "stop": 1753765133836, "duration": 8708}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set screen relay返回正确的不支持响应", "uid": "a9ae4477c0b948a6", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753766247545, "stop": 1753766256691, "duration": 9146}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set sim1 ringtone返回正确的不支持响应", "uid": "dc061218f29f8e09", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753766315363, "stop": 1753766324154, "duration": 8791}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set screen timeout返回正确的不支持响应", "uid": "33e1b9f38d32945b", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753766270460, "stop": 1753766279132, "duration": 8672}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to auto rotate screen settings返回正确的不支持响应", "uid": "c145d2c6d0bdd37b", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753765319585, "stop": 1753765332134, "duration": 12549}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试enable accelerate dialogue返回正确的不支持响应", "uid": "eacf804ad7a8f2d5", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753764937838, "stop": 1753764946850, "duration": 9012}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close equilibrium mode返回正确的不支持响应", "uid": "977a0552709ca7dd", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753761723925, "stop": 1753761733203, "duration": 9278}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set my fonts返回正确的不支持响应", "uid": "6dbcf1976884cdf8", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753766062593, "stop": 1753766071390, "duration": 8797}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set call back with last used sim返回正确的不支持响应", "uid": "ff43e38cf6639396", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753765717082, "stop": 1753765730096, "duration": 13014}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set gesture navigation返回正确的不支持响应", "uid": "ca8b062a81e447d0", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753765990851, "stop": 1753766003692, "duration": 12841}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set floating windows返回正确的不支持响应", "uid": "d1f215605f21a61a", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753765923195, "stop": 1753765931855, "duration": 8660}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to ai wallpaper generator settings返回正确的不支持响应", "uid": "3265a62e2f50eb16", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753765293770, "stop": 1753765305973, "duration": 12203}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open notification ringtone settings返回正确的不支持响应", "uid": "895239a3cabc886", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753765548511, "stop": 1753765560850, "duration": 12339}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to call notifications返回正确的不支持响应", "uid": "fd6f74a8afb4623b", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753765390724, "stop": 1753765403631, "duration": 12907}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set smart hub返回正确的不支持响应", "uid": "2d689da05d191cd8", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753766337954, "stop": 1753766346724, "duration": 8770}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close performance mode返回正确的不支持响应", "uid": "f5a5bd26d1bd1074", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753761746394, "stop": 1753761755312, "duration": 8918}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试enable zonetouch master返回正确的不支持响应", "uid": "4760d5acdc6b4ef1", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753765170109, "stop": 1753765178909, "duration": 8800}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable magic voice changer返回正确的不支持响应", "uid": "2184d35880254919", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753764758200, "stop": 1753764765984, "duration": 7784}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set flex-still mode返回正确的不支持响应", "uid": "2f290e7ec2fa59be", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753765878872, "stop": 1753765887490, "duration": 8618}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable hide notifications返回正确的不支持响应", "uid": "c2492372c4391305", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753761907303, "stop": 1753761916192, "duration": 8889}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set phantom v pen返回正确的不支持响应", "uid": "7498ca1bd63270a0", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753766152299, "stop": 1753766161117, "duration": 8818}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set my themes返回正确的不支持响应", "uid": "75008c9c41959d3b", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753766085120, "stop": 1753766093577, "duration": 8457}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to performance mode返回正确的不支持响应", "uid": "b9ad5f0890d7ff95", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753766497236, "stop": 1753766505493, "duration": 8257}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set screen to minimum brightness返回正确的不支持响应", "uid": "1d57845f41e6a5f8", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753766292819, "stop": 1753766301429, "duration": 8610}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set lockscreen passwords返回正确的不支持响应", "uid": "7373dd251d127d09", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753766040123, "stop": 1753766048747, "duration": 8624}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set ultra power saving返回正确的不支持响应", "uid": "c3dca0ed7665226e", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753766451006, "stop": 1753766460089, "duration": 9083}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set app notifications返回正确的不支持响应", "uid": "a934d12461417644", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753765668208, "stop": 1753765677125, "duration": 8917}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set folding screen zone返回正确的不支持响应", "uid": "881ccc6117d75d5b", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753765945477, "stop": 1753765953957, "duration": 8480}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable call rejection返回正确的不支持响应", "uid": "cb7dced3ed5dce81", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753761880191, "stop": 1753761893514, "duration": 13323}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set timezone返回正确的不支持响应", "uid": "e787218d61e83f91", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753766428807, "stop": 1753766437224, "duration": 8417}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to battery and power saving返回正确的不支持响应", "uid": "32fa194272e5fe43", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753765345752, "stop": 1753765354649, "duration": 8897}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set personal hotspot返回正确的不支持响应", "uid": "35097e6128df904c", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753766129315, "stop": 1753766138465, "duration": 9150}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable touch optimization返回正确的不支持响应", "uid": "73d90eb159607760", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753764823176, "stop": 1753764831729, "duration": 8553}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn off driving mode返回正确的不支持响应", "uid": "5e296627e553497f", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753766586941, "stop": 1753766595841, "duration": 8900}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试order a burger返回正确的不支持响应", "uid": "8600fc1e69aa8275", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753765574537, "stop": 1753765583369, "duration": 8832}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试open font family settings返回正确的不支持响应", "uid": "f7620d96e89fe416", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753765522695, "stop": 1753765534823, "duration": 12128}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable unfreeze返回正确的不支持响应", "uid": "771aecbdf39fcdf1", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753764845156, "stop": 1753764854156, "duration": 9000}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set smart panel返回正确的不支持响应", "uid": "c289979a8e3cbf4", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753766360497, "stop": 1753766369063, "duration": 8566}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试enable unfreeze返回正确的不支持响应", "uid": "e03fdcb88dd5fa05", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753765147303, "stop": 1753765155709, "duration": 8406}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn off show battery percentage返回正确的不支持响应", "uid": "17d22617af07582d", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753766609664, "stop": 1753766618172, "duration": 8508}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to battery usage返回正确的不支持响应", "uid": "6f7ba473d5caa2ff", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753765368389, "stop": 1753765377128, "duration": 8739}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable brightness locking返回正确的不支持响应", "uid": "ab574fd223ac3ccd", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753761857701, "stop": 1753761866488, "duration": 8787}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on driving mode返回正确的不支持响应", "uid": "6854011c63695983", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753766631818, "stop": 1753766640135, "duration": 8317}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable zonetouch master返回正确的不支持响应", "uid": "865b70c233569c0b", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753764867517, "stop": 1753764875770, "duration": 8253}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试how to set screenshots返回正确的不支持响应", "uid": "c8ecf6e0dcefd2e3", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753765219280, "stop": 1753765227782, "duration": 8502}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set flip case feature返回正确的不支持响应", "uid": "98bb8c35dbf6e67f", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753765901173, "stop": 1753765909427, "duration": 8254}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试check model information返回正确的不支持响应", "uid": "a56d8e321dbdf739", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753761677631, "stop": 1753761686636, "duration": 9005}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Enable Network Enhancement返回正确的不支持响应", "uid": "9b68c7b52f218f6f", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753765080707, "stop": 1753765088965, "duration": 8258}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试check my balance of sim1返回正确的不支持响应", "uid": "676b5a85a463de98", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753761700135, "stop": 1753761709886, "duration": 9751}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试how's the weather today?返回正确的不支持响应", "uid": "a3eadc9fba00cf71", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "failed", "time": {"start": 1753765192515, "stop": 1753765205101, "duration": 12586}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试order a takeaway返回正确的不支持响应", "uid": "9f6d55c7b99aa986", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753765597096, "stop": 1753765605451, "duration": 8355}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试enable auto pickup返回正确的不支持响应", "uid": "bca06f3ba9fb151a", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753764982371, "stop": 1753764991328, "duration": 8957}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set scheduled power on/off and restart返回正确的不支持响应", "uid": "c7a4a9bb9cd3d2d1", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753766202038, "stop": 1753766211140, "duration": 9102}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable running lock返回正确的不支持响应", "uid": "d872f1ccc0009a0b", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753764801669, "stop": 1753764810096, "duration": 8427}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set font size返回正确的不支持响应", "uid": "2d67f42529f4ba11", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753765967757, "stop": 1753765976842, "duration": 9085}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set color style返回正确的不支持响应", "uid": "afaa736411383736", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753765744290, "stop": 1753765752878, "duration": 8588}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试download basketball返回正确的不支持响应", "uid": "888dd68c71948402", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753764889004, "stop": 1753764902578, "duration": 13574}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set battery saver settings返回正确的不支持响应", "uid": "6d09de9067475c54", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753765690785, "stop": 1753765703376, "duration": 12591}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set date & time返回正确的不支持响应", "uid": "f098baef018951df", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753765833804, "stop": 1753765842611, "duration": 8807}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set languages返回正确的不支持响应", "uid": "523aa5be0a30d5f7", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753766017621, "stop": 1753766026454, "duration": 8833}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set edge mistouch prevention返回正确的不支持响应", "uid": "b790b402ce19d974", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753765856158, "stop": 1753765864716, "duration": 8558}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Enable Call on Hold返回正确的不支持响应", "uid": "865d969ffdc78faa", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753765026272, "stop": 1753765039213, "duration": 12941}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试enable all ai magic box features返回正确的不支持响应", "uid": "f12e7811277e974e", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753764959912, "stop": 1753764968912, "duration": 9000}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试increase settings for special functions返回正确的不支持响应", "uid": "757593af1b0f4165", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753765241506, "stop": 1753765253546, "duration": 12040}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set parallel windows返回正确的不支持响应", "uid": "feaafdb7aa1aa06", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753766107022, "stop": 1753766115543, "duration": 8521}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on high brightness mode返回正确的不支持响应", "uid": "246bdbd532071352", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753766654111, "stop": 1753766662935, "duration": 8824}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set special function返回正确的不支持响应", "uid": "fc74c441f7f7fac8", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753766382944, "stop": 1753766391498, "duration": 8554}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试close power saving mode返回正确的不支持响应", "uid": "8383bffd0b084967", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753761768665, "stop": 1753761777338, "duration": 8673}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Enable Call Rejection返回正确的不支持响应", "uid": "80224d74cee84492", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753765053607, "stop": 1753765066572, "duration": 12965}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable network enhancement返回正确的不支持响应", "uid": "7d3b9bd829e39087", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753764779947, "stop": 1753764788591, "duration": 8644}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试more settings返回正确的不支持响应", "uid": "b6291b5bdf4cb29d", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753765496809, "stop": 1753765508975, "duration": 12166}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试yandex eats返回正确的不支持响应", "uid": "1991f1f1174d50bb", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753766727520, "stop": 1753766736122, "duration": 8602}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set customized cover screen返回正确的不支持响应", "uid": "9bc73e51a27bea6f", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753765811458, "stop": 1753765819846, "duration": 8388}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable all ai magic box features返回正确的不支持响应", "uid": "7d7e356db129b7a7", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753761812917, "stop": 1753761822063, "duration": 9146}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试reset phone返回正确的不支持响应", "uid": "4566d78d4a8b1e24", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753765618958, "stop": 1753765632284, "duration": 13326}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set app auto rotate返回正确的不支持响应", "uid": "55306a20cf18accf", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753765645878, "stop": 1753765655018, "duration": 9140}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to high brightness mode settings返回正确的不支持响应", "uid": "ceced2591885abc6", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753765417843, "stop": 1753765430253, "duration": 12410}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试jump to lock screen notification and display settings返回正确的不支持响应", "uid": "8808f7aabaffe30e", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753765443894, "stop": 1753765456708, "duration": 12814}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switch to power saving mode返回正确的不支持响应", "uid": "509ae9b5f4ed11e1", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753766519465, "stop": 1753766528222, "duration": 8757}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试enable running lock返回正确的不支持响应", "uid": "ed1dcfed11432025", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753765102958, "stop": 1753765111240, "duration": 8282}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set screen refresh rate返回正确的不支持响应", "uid": "1b42bcf3479561c8", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753766224733, "stop": 1753766233136, "duration": 8403}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on show battery percentage返回正确的不支持响应", "uid": "6666371aa4b73f76", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753766676863, "stop": 1753766685957, "duration": 9094}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set split-screen apps返回正确的不支持响应", "uid": "94d5be3d7b73138a", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753766405436, "stop": 1753766415096, "duration": 9660}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set phone number返回正确的不支持响应", "uid": "f72ab1da7fdcbd71", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753766175014, "stop": 1753766188180, "duration": 13166}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试Voice setting page返回正确的不支持响应", "uid": "1b1585fb19777980", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753766700113, "stop": 1753766712922, "duration": 12809}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试switching charging speed返回正确的不支持响应", "uid": "a651e04c6a3ccba0", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753766542302, "stop": 1753766550938, "duration": 8636}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试enable brightness locking返回正确的不支持响应", "uid": "820e6fda2bc7ebdc", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753765004516, "stop": 1753765012789, "duration": 8273}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set compatibility mode返回正确的不支持响应", "uid": "1aef1ccd7dac8693", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753765767158, "stop": 1753765775913, "duration": 8755}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试set cover screen apps返回正确的不支持响应", "uid": "4f630659388f04a8", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753765789622, "stop": 1753765797895, "duration": 8273}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试disable auto pickup返回正确的不支持响应", "uid": "c2c1ab04ba6a67df", "parentUid": "30a2082256bcf63acf403c812111fb75", "status": "passed", "time": {"start": 1753761835778, "stop": 1753761844202, "duration": 8424}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "30a2082256bcf63acf403c812111fb75"}], "uid": "5209b93f42624278d4a59a103348bd3d"}]}