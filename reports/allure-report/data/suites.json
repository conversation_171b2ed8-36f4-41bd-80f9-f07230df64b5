{"uid": "98d3104e051c652961429bf95fa0b5d6", "name": "suites", "children": [{"name": "testcases.test_ella.unsupported_commands", "children": [{"name": "test_check_battery_information", "children": [{"name": "TestEllaCheckBatteryInformation", "children": [{"name": "测试check battery information返回正确的不支持响应", "uid": "b766f5a20f897743", "parentUid": "d28e0e4ff0e4838a6027dc47e3b3a670", "status": "passed", "time": {"start": 1753761631982, "stop": 1753761641998, "duration": 10016}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d28e0e4ff0e4838a6027dc47e3b3a670"}], "uid": "4c906a8c1578240415aebe31f410f234"}, {"name": "test_check_mobile_data_balance_of_sim", "children": [{"name": "TestEllaCheckMobileDataBalanceSim", "children": [{"name": "测试check mobile data balance of sim2返回正确的不支持响应", "uid": "2a28697021ec7d75", "parentUid": "2e50c8269684c4a73bb5b020614c2221", "status": "passed", "time": {"start": 1753761655231, "stop": 1753761664149, "duration": 8918}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2e50c8269684c4a73bb5b020614c2221"}], "uid": "ff56d23044bd5d499190e5ca173df4d3"}, {"name": "test_check_model_information", "children": [{"name": "TestEllaCheckModelInformation", "children": [{"name": "测试check model information返回正确的不支持响应", "uid": "a56d8e321dbdf739", "parentUid": "f7e3ca8a9343a9ed054471fe3e0356b4", "status": "passed", "time": {"start": 1753761677631, "stop": 1753761686636, "duration": 9005}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f7e3ca8a9343a9ed054471fe3e0356b4"}], "uid": "bf2cc0fa355876e3be500d0c5ba7a5f1"}, {"name": "test_check_my_balance_of_sim", "children": [{"name": "TestEllaCheckMyBalanceSim", "children": [{"name": "测试check my balance of sim1返回正确的不支持响应", "uid": "676b5a85a463de98", "parentUid": "f9995b28dafd2b1ed8762fedcb92c132", "status": "passed", "time": {"start": 1753761700135, "stop": 1753761709886, "duration": 9751}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f9995b28dafd2b1ed8762fedcb92c132"}], "uid": "ffd79a31b3c86841ac0cc6d5fa40f471"}, {"name": "test_close_equilibrium_mode", "children": [{"name": "TestEllaCloseEquilibriumMode", "children": [{"name": "测试close equilibrium mode返回正确的不支持响应", "uid": "977a0552709ca7dd", "parentUid": "2ca6423e5b9b0595a667d74916b87ae8", "status": "passed", "time": {"start": 1753761723925, "stop": 1753761733203, "duration": 9278}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2ca6423e5b9b0595a667d74916b87ae8"}], "uid": "cf0d3a245849cc3d2fb49b5919a2ca70"}, {"name": "test_close_performance_mode", "children": [{"name": "TestEllaClosePerformanceMode", "children": [{"name": "测试close performance mode返回正确的不支持响应", "uid": "f5a5bd26d1bd1074", "parentUid": "bfc98a3e31cf71b24d2c2664874d8f35", "status": "passed", "time": {"start": 1753761746394, "stop": 1753761755312, "duration": 8918}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bfc98a3e31cf71b24d2c2664874d8f35"}], "uid": "921c03c2af17c0dd8bb300d05ebc1435"}, {"name": "test_close_power_saving_mode", "children": [{"name": "TestEllaClosePowerSavingMode", "children": [{"name": "测试close power saving mode返回正确的不支持响应", "uid": "8383bffd0b084967", "parentUid": "d7c06ff8b4a17dd9ae62ae957d96f702", "status": "passed", "time": {"start": 1753761768665, "stop": 1753761777338, "duration": 8673}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d7c06ff8b4a17dd9ae62ae957d96f702"}], "uid": "de4693d49200ef3897dfa7e2a5aa8dbc"}, {"name": "test_disable_accelerate_dialogue", "children": [{"name": "TestEllaDisableAccelerateDialogue", "children": [{"name": "测试disable accelerate dialogue返回正确的不支持响应", "uid": "e3bf02bacdd2495b", "parentUid": "a12fb87a3ef6191f6c5649ce65173837", "status": "passed", "time": {"start": 1753761790803, "stop": 1753761799572, "duration": 8769}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a12fb87a3ef6191f6c5649ce65173837"}], "uid": "afbbdecfafcd893c4f7104365ff8935e"}, {"name": "test_disable_all_ai_magic_box_features", "children": [{"name": "TestEllaDisableAllAiMagicBoxFeatures", "children": [{"name": "测试disable all ai magic box features返回正确的不支持响应", "uid": "7d7e356db129b7a7", "parentUid": "696197cde92ea407d7be5d0ee1a43b1a", "status": "passed", "time": {"start": 1753761812917, "stop": 1753761822063, "duration": 9146}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "696197cde92ea407d7be5d0ee1a43b1a"}], "uid": "2b54914538eb635e492ddf923151d357"}, {"name": "test_disable_auto_pickup", "children": [{"name": "TestEllaDisableAutoPickup", "children": [{"name": "测试disable auto pickup返回正确的不支持响应", "uid": "c2c1ab04ba6a67df", "parentUid": "c20ac19fa762a9db14606dd531e82b8f", "status": "passed", "time": {"start": 1753761835778, "stop": 1753761844202, "duration": 8424}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c20ac19fa762a9db14606dd531e82b8f"}], "uid": "bf014ddab53633d98f8104639ce1bc1b"}, {"name": "test_disable_brightness_locking", "children": [{"name": "TestEllaDisableBrightnessLocking", "children": [{"name": "测试disable brightness locking返回正确的不支持响应", "uid": "ab574fd223ac3ccd", "parentUid": "858fef1ffc1503318aa70e9c72d7b438", "status": "passed", "time": {"start": 1753761857701, "stop": 1753761866488, "duration": 8787}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "858fef1ffc1503318aa70e9c72d7b438"}], "uid": "66c800c4edcde7ca93c0c123e93d1914"}, {"name": "test_disable_call_rejection", "children": [{"name": "TestEllaDisableCallRejection", "children": [{"name": "测试disable call rejection返回正确的不支持响应", "uid": "cb7dced3ed5dce81", "parentUid": "3f640e07903d6f89a09553a61d6690c3", "status": "passed", "time": {"start": 1753761880191, "stop": 1753761893514, "duration": 13323}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3f640e07903d6f89a09553a61d6690c3"}], "uid": "021f51382efc25258ec8ca7ef3573495"}, {"name": "test_disable_hide_notifications", "children": [{"name": "TestEllaDisableHideNotifications", "children": [{"name": "测试disable hide notifications返回正确的不支持响应", "uid": "c2492372c4391305", "parentUid": "9f132b3849693b61c2ca479bcafef282", "status": "passed", "time": {"start": 1753761907303, "stop": 1753761916192, "duration": 8889}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9f132b3849693b61c2ca479bcafef282"}], "uid": "7030498aad7e0e3d901754313db42a5c"}, {"name": "test_disable_magic_voice_changer", "children": [{"name": "TestEllaDisableMagicVoiceChanger", "children": [{"name": "测试disable magic voice changer返回正确的不支持响应", "uid": "2184d35880254919", "parentUid": "04c072a77ad52d90193c2ec6bc53a021", "status": "passed", "time": {"start": 1753764758200, "stop": 1753764765984, "duration": 7784}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "04c072a77ad52d90193c2ec6bc53a021"}], "uid": "6f0070b209221eadb3d6425a33ecbff1"}, {"name": "test_disable_network_enhancement", "children": [{"name": "TestEllaDisableNetworkEnhancement", "children": [{"name": "测试disable network enhancement返回正确的不支持响应", "uid": "7d3b9bd829e39087", "parentUid": "fe480ad7296777a27bc8f2d53aa0fccf", "status": "passed", "time": {"start": 1753764779947, "stop": 1753764788591, "duration": 8644}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fe480ad7296777a27bc8f2d53aa0fccf"}], "uid": "afc5376e9464fb8921d2fbcf9e0d3bbc"}, {"name": "test_disable_running_lock", "children": [{"name": "TestEllaDisableRunningLock", "children": [{"name": "测试disable running lock返回正确的不支持响应", "uid": "d872f1ccc0009a0b", "parentUid": "8fe90804f66731578ca4d4ba9283a64c", "status": "passed", "time": {"start": 1753764801669, "stop": 1753764810096, "duration": 8427}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8fe90804f66731578ca4d4ba9283a64c"}], "uid": "ad391be6683f94da1f0d53eaeb12f8a3"}, {"name": "test_disable_touch_optimization", "children": [{"name": "TestEllaDisableTouchOptimization", "children": [{"name": "测试disable touch optimization返回正确的不支持响应", "uid": "73d90eb159607760", "parentUid": "a61e1b3af122a2fe511826a7e99c2e1b", "status": "passed", "time": {"start": 1753764823176, "stop": 1753764831729, "duration": 8553}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a61e1b3af122a2fe511826a7e99c2e1b"}], "uid": "8d875ca58978089e7102b409d8215741"}, {"name": "test_disable_unfreeze", "children": [{"name": "TestEllaDisableUnfreeze", "children": [{"name": "测试disable unfreeze返回正确的不支持响应", "uid": "771aecbdf39fcdf1", "parentUid": "1e21c84af5bdef90eb5dee6bd5cf2635", "status": "passed", "time": {"start": 1753764845156, "stop": 1753764854156, "duration": 9000}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1e21c84af5bdef90eb5dee6bd5cf2635"}], "uid": "b1772b071366ab2c77148d9716c8dcd9"}, {"name": "test_disable_zonetouch_master", "children": [{"name": "TestEllaDisableZonetouchMaster", "children": [{"name": "测试disable zonetouch master返回正确的不支持响应", "uid": "865b70c233569c0b", "parentUid": "21d3787e184cbd6bdfc49c456af0ad04", "status": "passed", "time": {"start": 1753764867517, "stop": 1753764875770, "duration": 8253}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "21d3787e184cbd6bdfc49c456af0ad04"}], "uid": "f5c774dd2222acf0e4aa8f7b201f12b3"}, {"name": "test_download_basketball", "children": [{"name": "TestEllaDownloadBasketball", "children": [{"name": "测试download basketball返回正确的不支持响应", "uid": "888dd68c71948402", "parentUid": "98ae41f5cdd53a2d20bf8172efd26083", "status": "passed", "time": {"start": 1753764889004, "stop": 1753764902578, "duration": 13574}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "98ae41f5cdd53a2d20bf8172efd26083"}], "uid": "314bfe9c667cd809cb0cc507bfcd14fc"}, {"name": "test_driving_mode", "children": [{"name": "TestEllaDrivingMode", "children": [{"name": "测试driving mode返回正确的不支持响应", "uid": "f8e5b04c258276eb", "parentUid": "ef0bd4110e3386c3ad0f74515d6f200e", "status": "passed", "time": {"start": 1753764915935, "stop": 1753764924701, "duration": 8766}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ef0bd4110e3386c3ad0f74515d6f200e"}], "uid": "514aae0515740321638cc56a86d0bdc5"}, {"name": "test_enable_accelerate_dialogue", "children": [{"name": "TestEllaEnableAccelerateDialogue", "children": [{"name": "测试enable accelerate dialogue返回正确的不支持响应", "uid": "eacf804ad7a8f2d5", "parentUid": "eb6a89c6cd9a711ed9ac3e3c4a9f82e7", "status": "passed", "time": {"start": 1753764937838, "stop": 1753764946850, "duration": 9012}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "eb6a89c6cd9a711ed9ac3e3c4a9f82e7"}], "uid": "8c56ee95835e673fa28b34154afb4628"}, {"name": "test_enable_all_ai_magic_box_features", "children": [{"name": "TestEllaEnableAllAiMagicBoxFeatures", "children": [{"name": "测试enable all ai magic box features返回正确的不支持响应", "uid": "f12e7811277e974e", "parentUid": "8a587c12d6caed0b409b8971d49025ab", "status": "passed", "time": {"start": 1753764959912, "stop": 1753764968912, "duration": 9000}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8a587c12d6caed0b409b8971d49025ab"}], "uid": "68d379dd7f74dd5275f913b7eee03079"}, {"name": "test_enable_auto_pickup", "children": [{"name": "TestEllaEnableAutoPickup", "children": [{"name": "测试enable auto pickup返回正确的不支持响应", "uid": "bca06f3ba9fb151a", "parentUid": "97d17abb9dc1ccc8f46a34e06a32340c", "status": "passed", "time": {"start": 1753764982371, "stop": 1753764991328, "duration": 8957}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "97d17abb9dc1ccc8f46a34e06a32340c"}], "uid": "8c4650098d6069190cfba3c757ba45db"}, {"name": "test_enable_brightness_locking", "children": [{"name": "TestEllaEnableBrightnessLocking", "children": [{"name": "测试enable brightness locking返回正确的不支持响应", "uid": "820e6fda2bc7ebdc", "parentUid": "c400a86ee772ae2bb87a4d299fcffde3", "status": "passed", "time": {"start": 1753765004516, "stop": 1753765012789, "duration": 8273}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c400a86ee772ae2bb87a4d299fcffde3"}], "uid": "0e14ad27fc644f4f62c72d254e5d2d00"}, {"name": "test_enable_call_on_hold", "children": [{"name": "TestEllaEnableCallHold", "children": [{"name": "测试Enable Call on Hold返回正确的不支持响应", "uid": "865d969ffdc78faa", "parentUid": "6d79c2b8de7e0682e35d7e5c727c1ff8", "status": "passed", "time": {"start": 1753765026272, "stop": 1753765039213, "duration": 12941}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6d79c2b8de7e0682e35d7e5c727c1ff8"}], "uid": "de334526c374af0073eb9eb6ddb0997a"}, {"name": "test_enable_call_rejection", "children": [{"name": "TestEllaEnableCallRejection", "children": [{"name": "测试Enable Call Rejection返回正确的不支持响应", "uid": "80224d74cee84492", "parentUid": "e26b43428a954a94928f7a25e76abac9", "status": "passed", "time": {"start": 1753765053607, "stop": 1753765066572, "duration": 12965}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e26b43428a954a94928f7a25e76abac9"}], "uid": "366922e4cbf42ddefd029f732cdd77bf"}, {"name": "test_enable_network_enhancement", "children": [{"name": "TestEllaEnableNetworkEnhancement", "children": [{"name": "测试Enable Network Enhancement返回正确的不支持响应", "uid": "9b68c7b52f218f6f", "parentUid": "c12a817d493d41c56a647cb659a7bb8c", "status": "passed", "time": {"start": 1753765080707, "stop": 1753765088965, "duration": 8258}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c12a817d493d41c56a647cb659a7bb8c"}], "uid": "040b928633962c521baae26a6f7ac7c5"}, {"name": "test_enable_running_lock", "children": [{"name": "TestEllaEnableRunningLock", "children": [{"name": "测试enable running lock返回正确的不支持响应", "uid": "ed1dcfed11432025", "parentUid": "e1c78a96555072edad6f93b7ff33937c", "status": "passed", "time": {"start": 1753765102958, "stop": 1753765111240, "duration": 8282}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e1c78a96555072edad6f93b7ff33937c"}], "uid": "26ee37ce6707d6284177cfa0f1cae12c"}, {"name": "test_enable_touch_optimization", "children": [{"name": "TestEllaEnableTouchOptimization", "children": [{"name": "测试enable touch optimization返回正确的不支持响应", "uid": "144fa92bac6d7d3c", "parentUid": "d84bfce90dcde3b576c695692fca0fa8", "status": "passed", "time": {"start": 1753765125128, "stop": 1753765133836, "duration": 8708}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d84bfce90dcde3b576c695692fca0fa8"}], "uid": "76d94ee4c89c6674e70da8c74116bad6"}, {"name": "test_enable_unfreeze", "children": [{"name": "TestEllaEnableUnfreeze", "children": [{"name": "测试enable unfreeze返回正确的不支持响应", "uid": "e03fdcb88dd5fa05", "parentUid": "c801d69cec54af644c884ba704111151", "status": "passed", "time": {"start": 1753765147303, "stop": 1753765155709, "duration": 8406}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c801d69cec54af644c884ba704111151"}], "uid": "63e313e7547561b2fa987da4d6072437"}, {"name": "test_enable_zonetouch_master", "children": [{"name": "TestEllaEnableZonetouchMaster", "children": [{"name": "测试enable zonetouch master返回正确的不支持响应", "uid": "4760d5acdc6b4ef1", "parentUid": "99a67c39634063a502862411f9efdb5c", "status": "passed", "time": {"start": 1753765170109, "stop": 1753765178909, "duration": 8800}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "99a67c39634063a502862411f9efdb5c"}], "uid": "796eef53680203e2ca4133277e86230b"}, {"name": "test_how_s_the_weather_today", "children": [{"name": "TestEllaHowSWeatherToday", "children": [{"name": "测试how's the weather today?返回正确的不支持响应", "uid": "a3eadc9fba00cf71", "parentUid": "5b181d587aaceb1f4f643661a0ef2ee9", "status": "failed", "time": {"start": 1753765192515, "stop": 1753765205101, "duration": 12586}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5b181d587aaceb1f4f643661a0ef2ee9"}], "uid": "5fa1d6dc68c1fcce8e08513509b8807a"}, {"name": "test_how_to_set_screenshots", "children": [{"name": "TestEllaHowSetScreenshots", "children": [{"name": "测试how to set screenshots返回正确的不支持响应", "uid": "c8ecf6e0dcefd2e3", "parentUid": "4862cd77fa6584ab1cd401dd18e576cf", "status": "passed", "time": {"start": 1753765219280, "stop": 1753765227782, "duration": 8502}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4862cd77fa6584ab1cd401dd18e576cf"}], "uid": "3af4e120133a0419124a1c704c7d3199"}, {"name": "test_increase_settings_for_special_functions", "children": [{"name": "TestEllaIncreaseSettingsSpecialFunctions", "children": [{"name": "测试increase settings for special functions返回正确的不支持响应", "uid": "757593af1b0f4165", "parentUid": "47b335b99ed8a1ee8475ca4a6834e17a", "status": "passed", "time": {"start": 1753765241506, "stop": 1753765253546, "duration": 12040}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "47b335b99ed8a1ee8475ca4a6834e17a"}], "uid": "c3601014aaa999aff69c5872e32743b7"}, {"name": "test_jump_to_adaptive_brightness_settings", "children": [{"name": "TestEllaJumpAdaptiveBrightnessSettings", "children": [{"name": "测试jump to adaptive brightness settings返回正确的不支持响应", "uid": "26de3ee7bd066adc", "parentUid": "f92d66f2cfb1510e3614f32014b90cdf", "status": "passed", "time": {"start": 1753765267520, "stop": 1753765280068, "duration": 12548}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f92d66f2cfb1510e3614f32014b90cdf"}], "uid": "eda1255cba29f83ff1461094841f0f40"}, {"name": "test_jump_to_ai_wallpaper_generator_settings", "children": [{"name": "TestEllaJumpAiWallpaperGeneratorSettings", "children": [{"name": "测试jump to ai wallpaper generator settings返回正确的不支持响应", "uid": "3265a62e2f50eb16", "parentUid": "508a56999eea40b3b6eae200090d9ef7", "status": "passed", "time": {"start": 1753765293770, "stop": 1753765305973, "duration": 12203}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "508a56999eea40b3b6eae200090d9ef7"}], "uid": "952ea4e1b05a29ce2f17032769862fd2"}, {"name": "test_jump_to_auto_rotate_screen_settings", "children": [{"name": "TestEllaJumpAutoRotateScreenSettings", "children": [{"name": "测试jump to auto rotate screen settings返回正确的不支持响应", "uid": "c145d2c6d0bdd37b", "parentUid": "923f4dfcc1ff430310299406df4a76ad", "status": "passed", "time": {"start": 1753765319585, "stop": 1753765332134, "duration": 12549}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "923f4dfcc1ff430310299406df4a76ad"}], "uid": "2885405d4e301681acf1f9073b75196b"}, {"name": "test_jump_to_battery_and_power_saving", "children": [{"name": "TestEllaJumpBatteryPowerSaving", "children": [{"name": "测试jump to battery and power saving返回正确的不支持响应", "uid": "32fa194272e5fe43", "parentUid": "7b0af410a8bc7729423b66dbd86394a1", "status": "passed", "time": {"start": 1753765345752, "stop": 1753765354649, "duration": 8897}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7b0af410a8bc7729423b66dbd86394a1"}], "uid": "5e4b9eee9fae8ca49a205b0e08671105"}, {"name": "test_jump_to_battery_usage", "children": [{"name": "TestEllaJumpBatteryUsage", "children": [{"name": "测试jump to battery usage返回正确的不支持响应", "uid": "6f7ba473d5caa2ff", "parentUid": "e977b1d095b1f986aff6226776ded3c3", "status": "passed", "time": {"start": 1753765368389, "stop": 1753765377128, "duration": 8739}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e977b1d095b1f986aff6226776ded3c3"}], "uid": "379854b415d26ca0952c7565049546e2"}, {"name": "test_jump_to_call_notifications", "children": [{"name": "TestEllaJumpCallNotifications", "children": [{"name": "测试jump to call notifications返回正确的不支持响应", "uid": "fd6f74a8afb4623b", "parentUid": "e1d634c784c8a3eaf84c787e520a993c", "status": "passed", "time": {"start": 1753765390724, "stop": 1753765403631, "duration": 12907}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e1d634c784c8a3eaf84c787e520a993c"}], "uid": "0d6d5e9fa27b112c5e4a5c892fa26625"}, {"name": "test_jump_to_high_brightness_mode_settings", "children": [{"name": "TestEllaJumpHighBrightnessModeSettings", "children": [{"name": "测试jump to high brightness mode settings返回正确的不支持响应", "uid": "ceced2591885abc6", "parentUid": "968c23cffad77650f73adb57ed35e44e", "status": "passed", "time": {"start": 1753765417843, "stop": 1753765430253, "duration": 12410}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "968c23cffad77650f73adb57ed35e44e"}], "uid": "f96efa82b36a271078aa933d212ff1e1"}, {"name": "test_jump_to_lock_screen_notification_and_display_settings", "children": [{"name": "TestEllaOpenSettings", "children": [{"name": "测试jump to lock screen notification and display settings返回正确的不支持响应", "uid": "8808f7aabaffe30e", "parentUid": "6e6a65b648407dd3ea18fb96b915dace", "status": "passed", "time": {"start": 1753765443894, "stop": 1753765456708, "duration": 12814}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6e6a65b648407dd3ea18fb96b915dace"}], "uid": "a57e12d4c6edf0c5f1e60f981492cd0f"}, {"name": "test_jump_to_notifications_and_status_bar_settings", "children": [{"name": "TestEllaJumpNotificationsStatusBarSettings", "children": [{"name": "测试jump to notifications and status bar settings返回正确的不支持响应", "uid": "c297d430a8b59844", "parentUid": "5891939ff40959b2d8e13bfa76678a88", "status": "passed", "time": {"start": 1753765470565, "stop": 1753765483040, "duration": 12475}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5891939ff40959b2d8e13bfa76678a88"}], "uid": "843f8e4d40a46af764e0186551ea2eab"}, {"name": "test_more_settings", "children": [{"name": "TestEllaMoreSettings", "children": [{"name": "测试more settings返回正确的不支持响应", "uid": "b6291b5bdf4cb29d", "parentUid": "5158adca762a1833ee225b49be689b4b", "status": "passed", "time": {"start": 1753765496809, "stop": 1753765508975, "duration": 12166}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5158adca762a1833ee225b49be689b4b"}], "uid": "2678afdbcf3cd1c2f7304aab68dff13e"}, {"name": "test_open_font_family_settings", "children": [{"name": "TestEllaOpenSettings", "children": [{"name": "测试open font family settings返回正确的不支持响应", "uid": "f7620d96e89fe416", "parentUid": "3abee97ea9125b112c9768709b230d6c", "status": "passed", "time": {"start": 1753765522695, "stop": 1753765534823, "duration": 12128}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3abee97ea9125b112c9768709b230d6c"}], "uid": "c37be023aa1668dd5137d98115ed22ee"}, {"name": "test_open_notification_ringtone_settings", "children": [{"name": "TestEllaOpenSettings", "children": [{"name": "测试open notification ringtone settings返回正确的不支持响应", "uid": "895239a3cabc886", "parentUid": "1547bf4ebc0f2064b90de7ee9d4b0d9a", "status": "passed", "time": {"start": 1753765548511, "stop": 1753765560850, "duration": 12339}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1547bf4ebc0f2064b90de7ee9d4b0d9a"}], "uid": "e9bb1812204e529485c6767282c66ee4"}, {"name": "test_order_a_burger", "children": [{"name": "TestEllaOrderBurger", "children": [{"name": "测试order a burger返回正确的不支持响应", "uid": "8600fc1e69aa8275", "parentUid": "caf4b95867e28da5919f2eca0a324167", "status": "passed", "time": {"start": 1753765574537, "stop": 1753765583369, "duration": 8832}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "caf4b95867e28da5919f2eca0a324167"}], "uid": "5866702b17345a70f73c15e82c35a889"}, {"name": "test_order_a_takeaway", "children": [{"name": "TestEllaOrderTakeaway", "children": [{"name": "测试order a takeaway返回正确的不支持响应", "uid": "9f6d55c7b99aa986", "parentUid": "bee4551900f0d6522500f0c802a47f62", "status": "passed", "time": {"start": 1753765597096, "stop": 1753765605451, "duration": 8355}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bee4551900f0d6522500f0c802a47f62"}], "uid": "d94ef1da6fdbed2a2a316ba39ec7f816"}, {"name": "test_reset_phone", "children": [{"name": "TestEllaResetPhone", "children": [{"name": "测试reset phone返回正确的不支持响应", "uid": "4566d78d4a8b1e24", "parentUid": "cac15668a449dfa5284ac8751a8bc088", "status": "passed", "time": {"start": 1753765618958, "stop": 1753765632284, "duration": 13326}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cac15668a449dfa5284ac8751a8bc088"}], "uid": "20f77fb14c171a00344aefe112819e8f"}, {"name": "test_set_app_auto_rotate", "children": [{"name": "TestEllaSetAppAutoRotate", "children": [{"name": "测试set app auto rotate返回正确的不支持响应", "uid": "55306a20cf18accf", "parentUid": "5ec31d73ee5a8cbd16b741e2a0c12725", "status": "passed", "time": {"start": 1753765645878, "stop": 1753765655018, "duration": 9140}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5ec31d73ee5a8cbd16b741e2a0c12725"}], "uid": "c4e1b97dbe16d5e25caab90c80f85213"}, {"name": "test_set_app_notifications", "children": [{"name": "TestEllaSetAppNotifications", "children": [{"name": "测试set app notifications返回正确的不支持响应", "uid": "a934d12461417644", "parentUid": "e2b37df150554a2656df5f000b5f3877", "status": "passed", "time": {"start": 1753765668208, "stop": 1753765677125, "duration": 8917}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e2b37df150554a2656df5f000b5f3877"}], "uid": "c477cad1f43ef2333b7849edf5ce05ed"}, {"name": "test_set_battery_saver_settings", "children": [{"name": "TestEllaSetBatterySaverSettings", "children": [{"name": "测试set battery saver settings返回正确的不支持响应", "uid": "6d09de9067475c54", "parentUid": "c420f6bca101c62ba94f9e8e7f31a573", "status": "passed", "time": {"start": 1753765690785, "stop": 1753765703376, "duration": 12591}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c420f6bca101c62ba94f9e8e7f31a573"}], "uid": "8b4c47455f601b3d7d6f1e100772a44a"}, {"name": "test_set_call_back_with_last_used_sim", "children": [{"name": "TestEllaSetCallBackLastUsedSim", "children": [{"name": "测试set call back with last used sim返回正确的不支持响应", "uid": "ff43e38cf6639396", "parentUid": "4c0f190c6f4aee497792a6d856a0035a", "status": "passed", "time": {"start": 1753765717082, "stop": 1753765730096, "duration": 13014}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4c0f190c6f4aee497792a6d856a0035a"}], "uid": "7cea4873dfe6764b25d4b6368eec9cfb"}, {"name": "test_set_color_style", "children": [{"name": "TestEllaSetColorStyle", "children": [{"name": "测试set color style返回正确的不支持响应", "uid": "afaa736411383736", "parentUid": "a93fd41464aaeca8c5187ded9998a8cf", "status": "passed", "time": {"start": 1753765744290, "stop": 1753765752878, "duration": 8588}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a93fd41464aaeca8c5187ded9998a8cf"}], "uid": "ba0686e84222e058748253df8047a50b"}, {"name": "test_set_compatibility_mode", "children": [{"name": "TestEllaSetCompatibilityMode", "children": [{"name": "测试set compatibility mode返回正确的不支持响应", "uid": "1aef1ccd7dac8693", "parentUid": "3b4b879cb25f4ec1c9e5f56f7b979c37", "status": "passed", "time": {"start": 1753765767158, "stop": 1753765775913, "duration": 8755}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3b4b879cb25f4ec1c9e5f56f7b979c37"}], "uid": "1909cdae019ead080ae02a578b4d7316"}, {"name": "test_set_cover_screen_apps", "children": [{"name": "TestEllaSetCoverScreenApps", "children": [{"name": "测试set cover screen apps返回正确的不支持响应", "uid": "4f630659388f04a8", "parentUid": "37a4a9b94837c06f3d0d560b86bbe29b", "status": "passed", "time": {"start": 1753765789622, "stop": 1753765797895, "duration": 8273}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "37a4a9b94837c06f3d0d560b86bbe29b"}], "uid": "5df35bd6f80d4c6657cbae8b27064aab"}, {"name": "test_set_customized_cover_screen", "children": [{"name": "TestEllaSetCustomizedCoverScreen", "children": [{"name": "测试set customized cover screen返回正确的不支持响应", "uid": "9bc73e51a27bea6f", "parentUid": "4d3fece58e4f70510cd292befe33b96a", "status": "passed", "time": {"start": 1753765811458, "stop": 1753765819846, "duration": 8388}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4d3fece58e4f70510cd292befe33b96a"}], "uid": "3fa52a12d7acae5645ea3aa75477d44b"}, {"name": "test_set_date_time", "children": [{"name": "TestEllaSetDateTime", "children": [{"name": "测试set date & time返回正确的不支持响应", "uid": "f098baef018951df", "parentUid": "9d950448d0ff30907a7af780f114a130", "status": "passed", "time": {"start": 1753765833804, "stop": 1753765842611, "duration": 8807}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9d950448d0ff30907a7af780f114a130"}], "uid": "8c30598a8b1f5940667edde71b30791c"}, {"name": "test_set_edge_mistouch_prevention", "children": [{"name": "TestEllaSetEdgeMistouchPrevention", "children": [{"name": "测试set edge mistouch prevention返回正确的不支持响应", "uid": "b790b402ce19d974", "parentUid": "dada83ec1ab33079d5f86d4ac7ee1f04", "status": "passed", "time": {"start": 1753765856158, "stop": 1753765864716, "duration": 8558}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "dada83ec1ab33079d5f86d4ac7ee1f04"}], "uid": "a30de810322f2776f8c899c615c4be77"}, {"name": "test_set_flex_still_mode", "children": [{"name": "TestEllaSetFlexStillMode", "children": [{"name": "测试set flex-still mode返回正确的不支持响应", "uid": "2f290e7ec2fa59be", "parentUid": "a51d316b425964f68fef53807195a46f", "status": "passed", "time": {"start": 1753765878872, "stop": 1753765887490, "duration": 8618}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a51d316b425964f68fef53807195a46f"}], "uid": "0100b1b2aa4c49467859f43e6353bedb"}, {"name": "test_set_flip_case_feature", "children": [{"name": "TestEllaSetFlipCaseFeature", "children": [{"name": "测试set flip case feature返回正确的不支持响应", "uid": "98bb8c35dbf6e67f", "parentUid": "d5b01634d272a358f06ff35ca4ca4a23", "status": "passed", "time": {"start": 1753765901173, "stop": 1753765909427, "duration": 8254}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d5b01634d272a358f06ff35ca4ca4a23"}], "uid": "659c6f22cfed2953c4ba8d4b8dd7294a"}, {"name": "test_set_floating_windows", "children": [{"name": "TestEllaSetFloatingWindows", "children": [{"name": "测试set floating windows返回正确的不支持响应", "uid": "d1f215605f21a61a", "parentUid": "e15e2e981396bcf4d57ba068a480c5b1", "status": "passed", "time": {"start": 1753765923195, "stop": 1753765931855, "duration": 8660}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e15e2e981396bcf4d57ba068a480c5b1"}], "uid": "1dc2787970a8d9c589a8ae300f462858"}, {"name": "test_set_folding_screen_zone", "children": [{"name": "TestEllaSetFoldingScreenZone", "children": [{"name": "测试set folding screen zone返回正确的不支持响应", "uid": "881ccc6117d75d5b", "parentUid": "47c139cd34b0a780094ffb98e1812572", "status": "passed", "time": {"start": 1753765945477, "stop": 1753765953957, "duration": 8480}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "47c139cd34b0a780094ffb98e1812572"}], "uid": "45d2d7d6fad9b4da39cc6dac0c4fa337"}, {"name": "test_set_font_size", "children": [{"name": "TestEllaSetFontSize", "children": [{"name": "测试set font size返回正确的不支持响应", "uid": "2d67f42529f4ba11", "parentUid": "80b6fcd56edd3e33d66094a25cc5d31b", "status": "passed", "time": {"start": 1753765967757, "stop": 1753765976842, "duration": 9085}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "80b6fcd56edd3e33d66094a25cc5d31b"}], "uid": "9a3389ccf2eca5621def99072303502f"}, {"name": "test_set_gesture_navigation", "children": [{"name": "TestEllaSetGestureNavigation", "children": [{"name": "测试set gesture navigation返回正确的不支持响应", "uid": "ca8b062a81e447d0", "parentUid": "5a5a307b4aee14702733cda564e2594e", "status": "passed", "time": {"start": 1753765990851, "stop": 1753766003692, "duration": 12841}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5a5a307b4aee14702733cda564e2594e"}], "uid": "f45d715ad98a0594e567c013238fb055"}, {"name": "test_set_languages", "children": [{"name": "TestEllaSetLanguages", "children": [{"name": "测试set languages返回正确的不支持响应", "uid": "523aa5be0a30d5f7", "parentUid": "3e28dcd9a9cdb251320f0e3b7e655a4d", "status": "passed", "time": {"start": 1753766017621, "stop": 1753766026454, "duration": 8833}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3e28dcd9a9cdb251320f0e3b7e655a4d"}], "uid": "eedc9f2088f9e7ed345a41dcf938b3aa"}, {"name": "test_set_lockscreen_passwords", "children": [{"name": "TestEllaSetLockscreenPasswords", "children": [{"name": "测试set lockscreen passwords返回正确的不支持响应", "uid": "7373dd251d127d09", "parentUid": "41dd05458efc06498e30ee8a35ffd98f", "status": "passed", "time": {"start": 1753766040123, "stop": 1753766048747, "duration": 8624}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "41dd05458efc06498e30ee8a35ffd98f"}], "uid": "62cf905659379d62ce72a50cba40e0f8"}, {"name": "test_set_my_fonts", "children": [{"name": "TestEllaSetMyFonts", "children": [{"name": "测试set my fonts返回正确的不支持响应", "uid": "6dbcf1976884cdf8", "parentUid": "3feb62634faa8c2680759a67a0f0b2df", "status": "passed", "time": {"start": 1753766062593, "stop": 1753766071390, "duration": 8797}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3feb62634faa8c2680759a67a0f0b2df"}], "uid": "64427e107c50bb3a09e1b67c0bf1e141"}, {"name": "test_set_my_themes", "children": [{"name": "TestEllaSetMyThemes", "children": [{"name": "测试set my themes返回正确的不支持响应", "uid": "75008c9c41959d3b", "parentUid": "b86797063b9d865d350ec21ac7112b5f", "status": "passed", "time": {"start": 1753766085120, "stop": 1753766093577, "duration": 8457}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b86797063b9d865d350ec21ac7112b5f"}], "uid": "716eecd28b7d4b5ede25b5df0e5507c2"}, {"name": "test_set_parallel_windows", "children": [{"name": "TestEllaSetParallelWindows", "children": [{"name": "测试set parallel windows返回正确的不支持响应", "uid": "feaafdb7aa1aa06", "parentUid": "e9b410d4bf477a3418fde54f839d7e6d", "status": "passed", "time": {"start": 1753766107022, "stop": 1753766115543, "duration": 8521}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e9b410d4bf477a3418fde54f839d7e6d"}], "uid": "55c6cd519ecfe75eb797fa2c66d0d75f"}, {"name": "test_set_personal_hotspot", "children": [{"name": "TestEllaSetPersonalHotspot", "children": [{"name": "测试set personal hotspot返回正确的不支持响应", "uid": "35097e6128df904c", "parentUid": "b3579038871378a6fdcc9bb20849bdf1", "status": "passed", "time": {"start": 1753766129315, "stop": 1753766138465, "duration": 9150}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b3579038871378a6fdcc9bb20849bdf1"}], "uid": "b482fd86e6dab754c48a5bf619de5afb"}, {"name": "test_set_phantom_v_pen", "children": [{"name": "TestEllaSetPhantomVPen", "children": [{"name": "测试set phantom v pen返回正确的不支持响应", "uid": "7498ca1bd63270a0", "parentUid": "7c5353b9ef36136e92a92708b7e45bb3", "status": "passed", "time": {"start": 1753766152299, "stop": 1753766161117, "duration": 8818}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7c5353b9ef36136e92a92708b7e45bb3"}], "uid": "107e8fe279ca1fd2b153352fda86fb4a"}, {"name": "test_set_phone_number", "children": [{"name": "TestEllaSetPhoneNumber", "children": [{"name": "测试set phone number返回正确的不支持响应", "uid": "f72ab1da7fdcbd71", "parentUid": "96b22c407c943460e8486fc825169a80", "status": "passed", "time": {"start": 1753766175014, "stop": 1753766188180, "duration": 13166}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "96b22c407c943460e8486fc825169a80"}], "uid": "e9cd1fa5db40d2ec604808d9c497fadb"}, {"name": "test_set_scheduled_power_on_off_and_restart", "children": [{"name": "TestEllaSetScheduledPowerOffRestart", "children": [{"name": "测试set scheduled power on/off and restart返回正确的不支持响应", "uid": "c7a4a9bb9cd3d2d1", "parentUid": "58b82848dae0adadd98c95016e0b8711", "status": "passed", "time": {"start": 1753766202038, "stop": 1753766211140, "duration": 9102}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "58b82848dae0adadd98c95016e0b8711"}], "uid": "e6b0ed51c0d597afdb5bf8c4e6af53c3"}, {"name": "test_set_screen_refresh_rate", "children": [{"name": "TestEllaSetScreenRefreshRate", "children": [{"name": "测试set screen refresh rate返回正确的不支持响应", "uid": "1b42bcf3479561c8", "parentUid": "3e9009cde7fd47070b3529a63de695c3", "status": "passed", "time": {"start": 1753766224733, "stop": 1753766233136, "duration": 8403}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3e9009cde7fd47070b3529a63de695c3"}], "uid": "c9de8a50bcaa3dc3fd1b3a9409aa8ba0"}, {"name": "test_set_screen_relay", "children": [{"name": "TestEllaSetScreenRelay", "children": [{"name": "测试set screen relay返回正确的不支持响应", "uid": "a9ae4477c0b948a6", "parentUid": "61fe1e0731921d580561f77ccf2de7a2", "status": "passed", "time": {"start": 1753766247545, "stop": 1753766256691, "duration": 9146}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "61fe1e0731921d580561f77ccf2de7a2"}], "uid": "7ebda1dd475915217421f499f80df8c5"}, {"name": "test_set_screen_timeout", "children": [{"name": "TestEllaSetScreenTimeout", "children": [{"name": "测试set screen timeout返回正确的不支持响应", "uid": "33e1b9f38d32945b", "parentUid": "3f7e6a161af6de02774a99275b95e1d7", "status": "passed", "time": {"start": 1753766270460, "stop": 1753766279132, "duration": 8672}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3f7e6a161af6de02774a99275b95e1d7"}], "uid": "3ad3475cd36fab63233e05c3002bb8c5"}, {"name": "test_set_screen_to_minimum_brightness", "children": [{"name": "TestEllaSetScreenMinimumBrightness", "children": [{"name": "测试set screen to minimum brightness返回正确的不支持响应", "uid": "1d57845f41e6a5f8", "parentUid": "70cd5196189a628ba70f4e189ab3ed2a", "status": "passed", "time": {"start": 1753766292819, "stop": 1753766301429, "duration": 8610}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "70cd5196189a628ba70f4e189ab3ed2a"}], "uid": "ccff2bc4a244de015f51207ad9fb9973"}, {"name": "test_set_sim_ringtone", "children": [{"name": "TestEllaSetSimRingtone", "children": [{"name": "测试set sim1 ringtone返回正确的不支持响应", "uid": "dc061218f29f8e09", "parentUid": "b540694383c716efd22be6cf332cff66", "status": "passed", "time": {"start": 1753766315363, "stop": 1753766324154, "duration": 8791}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b540694383c716efd22be6cf332cff66"}], "uid": "f521b50083932bc320d541b59f4fa12e"}, {"name": "test_set_smart_hub", "children": [{"name": "TestEllaSetSmartHub", "children": [{"name": "测试set smart hub返回正确的不支持响应", "uid": "2d689da05d191cd8", "parentUid": "9982a7cb76e7edde540c33f77bcb4131", "status": "passed", "time": {"start": 1753766337954, "stop": 1753766346724, "duration": 8770}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9982a7cb76e7edde540c33f77bcb4131"}], "uid": "9159365ae1f13aa7202a2ecabae352cd"}, {"name": "test_set_smart_panel", "children": [{"name": "TestEllaSetSmartPanel", "children": [{"name": "测试set smart panel返回正确的不支持响应", "uid": "c289979a8e3cbf4", "parentUid": "8186149225876d2c7f48b35cdec99dd3", "status": "passed", "time": {"start": 1753766360497, "stop": 1753766369063, "duration": 8566}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8186149225876d2c7f48b35cdec99dd3"}], "uid": "6d2c866f4f10ac8e5f2e0399839eca9f"}, {"name": "test_set_special_function", "children": [{"name": "TestEllaSetSpecialFunction", "children": [{"name": "测试set special function返回正确的不支持响应", "uid": "fc74c441f7f7fac8", "parentUid": "a485e9dc82964f874c4ca66999bcf15a", "status": "passed", "time": {"start": 1753766382944, "stop": 1753766391498, "duration": 8554}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a485e9dc82964f874c4ca66999bcf15a"}], "uid": "1385459e098e23ca404584fba544f87b"}, {"name": "test_set_split_screen_apps", "children": [{"name": "TestEllaSetSplitScreenApps", "children": [{"name": "测试set split-screen apps返回正确的不支持响应", "uid": "94d5be3d7b73138a", "parentUid": "e704a416305aeb7b8861f78da3d399fd", "status": "passed", "time": {"start": 1753766405436, "stop": 1753766415096, "duration": 9660}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e704a416305aeb7b8861f78da3d399fd"}], "uid": "18aa5f1d09e55e245cdba84af89bdb4f"}, {"name": "test_set_timezone", "children": [{"name": "TestEllaSetTimezone", "children": [{"name": "测试set timezone返回正确的不支持响应", "uid": "e787218d61e83f91", "parentUid": "f68e969f0dc6fa17bc3cbb1c76e0468d", "status": "passed", "time": {"start": 1753766428807, "stop": 1753766437224, "duration": 8417}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f68e969f0dc6fa17bc3cbb1c76e0468d"}], "uid": "6b5e2d316297d46c06b128e02645ba18"}, {"name": "test_set_ultra_power_saving", "children": [{"name": "TestEllaSetUltraPowerSaving", "children": [{"name": "测试set ultra power saving返回正确的不支持响应", "uid": "c3dca0ed7665226e", "parentUid": "c853a846bf2e6118074af640a3bdfded", "status": "passed", "time": {"start": 1753766451006, "stop": 1753766460089, "duration": 9083}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c853a846bf2e6118074af640a3bdfded"}], "uid": "516f9e12e9fe59099bceb66c299b4b06"}, {"name": "test_switch_to_equilibrium_mode", "children": [{"name": "TestEllaSwitchEquilibriumMode", "children": [{"name": "测试switch to equilibrium mode返回正确的不支持响应", "uid": "ce8424deaa12d0d3", "parentUid": "0bfb9433b5ffe1874f0d5456da733833", "status": "passed", "time": {"start": 1753766474322, "stop": 1753766483379, "duration": 9057}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0bfb9433b5ffe1874f0d5456da733833"}], "uid": "6dfd47afc83ade6b7e64a599884aa82b"}, {"name": "test_switch_to_performance_mode", "children": [{"name": "TestEllaSwitchPerformanceMode", "children": [{"name": "测试switch to performance mode返回正确的不支持响应", "uid": "b9ad5f0890d7ff95", "parentUid": "e8f8d6344d1896b53428623c96ae9737", "status": "passed", "time": {"start": 1753766497236, "stop": 1753766505493, "duration": 8257}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e8f8d6344d1896b53428623c96ae9737"}], "uid": "660b4eae367594166e98133010b58c55"}, {"name": "test_switch_to_power_saving_mode", "children": [{"name": "TestEllaSwitchPowerSavingMode", "children": [{"name": "测试switch to power saving mode返回正确的不支持响应", "uid": "509ae9b5f4ed11e1", "parentUid": "69d2b561fc70829d0f0c3bcbc22bd24b", "status": "passed", "time": {"start": 1753766519465, "stop": 1753766528222, "duration": 8757}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "69d2b561fc70829d0f0c3bcbc22bd24b"}], "uid": "89fed13c0557d811b5a98a81c18249c0"}, {"name": "test_switching_charging_speed", "children": [{"name": "TestEllaSwitchingChargingSpeed", "children": [{"name": "测试switching charging speed返回正确的不支持响应", "uid": "a651e04c6a3ccba0", "parentUid": "364923db55b7a4749bd0f0a3d56b478a", "status": "passed", "time": {"start": 1753766542302, "stop": 1753766550938, "duration": 8636}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "364923db55b7a4749bd0f0a3d56b478a"}], "uid": "2bc0a442c3b51933b3e020072f001bf8"}, {"name": "test_the_second", "children": [{"name": "TestEllaSecond", "children": [{"name": "测试the second返回正确的不支持响应", "uid": "487f42ee1097884f", "parentUid": "ba446261c47bba6a43d497764fa39a1e", "status": "passed", "time": {"start": 1753766564387, "stop": 1753766572770, "duration": 8383}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ba446261c47bba6a43d497764fa39a1e"}], "uid": "3ef96941774e396d5f61e6c374c2a964"}, {"name": "test_turn_off_driving_mode", "children": [{"name": "TestEllaTurnOffDrivingMode", "children": [{"name": "测试turn off driving mode返回正确的不支持响应", "uid": "5e296627e553497f", "parentUid": "24ecd251ff3a56a4c2b20fac8ab55f2f", "status": "passed", "time": {"start": 1753766586941, "stop": 1753766595841, "duration": 8900}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "24ecd251ff3a56a4c2b20fac8ab55f2f"}], "uid": "ca08b2eb3eff172bf5d579af818a108f"}, {"name": "test_turn_off_show_battery_percentage", "children": [{"name": "TestEllaTurnOffShowBatteryPercentage", "children": [{"name": "测试turn off show battery percentage返回正确的不支持响应", "uid": "17d22617af07582d", "parentUid": "a93695196bd86c4de1585781fedbe080", "status": "passed", "time": {"start": 1753766609664, "stop": 1753766618172, "duration": 8508}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a93695196bd86c4de1585781fedbe080"}], "uid": "492b5eb198aa5b8127740d2701bc4490"}, {"name": "test_turn_on_driving_mode", "children": [{"name": "TestEllaTurnDrivingMode", "children": [{"name": "测试turn on driving mode返回正确的不支持响应", "uid": "6854011c63695983", "parentUid": "6160fde1e3814087ef49c469733a5e59", "status": "passed", "time": {"start": 1753766631818, "stop": 1753766640135, "duration": 8317}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6160fde1e3814087ef49c469733a5e59"}], "uid": "7dbd97d94de7ac9fc92ce34bec95b544"}, {"name": "test_turn_on_high_brightness_mode", "children": [{"name": "TestEllaTurnHighBrightnessMode", "children": [{"name": "测试turn on high brightness mode返回正确的不支持响应", "uid": "246bdbd532071352", "parentUid": "afa769cc76891525f7fca4f496a4aa9e", "status": "passed", "time": {"start": 1753766654111, "stop": 1753766662935, "duration": 8824}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "afa769cc76891525f7fca4f496a4aa9e"}], "uid": "9751732cdae9d7a072c84060577699cd"}, {"name": "test_turn_on_show_battery_percentage", "children": [{"name": "TestEllaTurnShowBatteryPercentage", "children": [{"name": "测试turn on show battery percentage返回正确的不支持响应", "uid": "6666371aa4b73f76", "parentUid": "9a14e86a2195f01365f057ce6c9b9e71", "status": "passed", "time": {"start": 1753766676863, "stop": 1753766685957, "duration": 9094}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9a14e86a2195f01365f057ce6c9b9e71"}], "uid": "d5d7ce8427ad486d072c9aae7e2d5b3f"}, {"name": "test_voice_setting_page", "children": [{"name": "TestEllaVoiceSettingPage", "children": [{"name": "测试Voice setting page返回正确的不支持响应", "uid": "1b1585fb19777980", "parentUid": "087c3d15647e1625a6f4e506048dafc7", "status": "passed", "time": {"start": 1753766700113, "stop": 1753766712922, "duration": 12809}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "087c3d15647e1625a6f4e506048dafc7"}], "uid": "20224dbf8d73e834d2be6a17094deb05"}, {"name": "test_yandex_eats", "children": [{"name": "TestEllaYandexEats", "children": [{"name": "测试yandex eats返回正确的不支持响应", "uid": "1991f1f1174d50bb", "parentUid": "3c01adf30480b292f5f2b75550755b66", "status": "passed", "time": {"start": 1753766727520, "stop": 1753766736122, "duration": 8602}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3c01adf30480b292f5f2b75550755b66"}], "uid": "e099b3554c54efdee17091c4c12f77c7"}], "uid": "09cb3650ff0a2cc2af23d31dd3c975a2"}]}