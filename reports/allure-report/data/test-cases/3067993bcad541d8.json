{"uid": "3067993bcad541d8", "name": "测试open contact命令", "fullName": "testcases.test_ella.test_open_contact.TestEllaContactCommandConcise#test_open_contact", "historyId": "9f28736d78e3cebb5eddb51e5e9dc3c8", "time": {"start": 1753193187796, "stop": 1753193196884, "duration": 9088}, "description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "descriptionHtml": "<p>使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753193144009, "stop": 1753193144382, "duration": 373}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": false}, {"name": "ella_app", "time": {"start": 1753193182174, "stop": 1753193187794, "duration": 5620}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1753193187794, "stop": 1753193187794, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": false}], "testStage": {"description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "status": "passed", "steps": [{"name": "执行命令: open contact", "time": {"start": 1753193187796, "stop": 1753193196526, "duration": 8730}, "status": "passed", "steps": [{"name": "执行联系人命令: open contact", "time": {"start": 1753193187796, "stop": 1753193196526, "duration": 8730}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": false}, {"name": "验证联系人命令结果", "time": {"start": 1753193196526, "stop": 1753193196526, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": false}], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 2, "attachmentStep": false, "hasContent": true}, {"name": "验证响应包含Done", "time": {"start": 1753193196526, "stop": 1753193196530, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": false}, {"name": "验证Dalier应用已打开", "time": {"start": 1753193196530, "stop": 1753193196530, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1753193196530, "stop": 1753193196883, "duration": 353}, "status": "passed", "steps": [], "attachments": [{"uid": "579512259ed4f20c", "name": "测试总结", "source": "579512259ed4f20c.txt", "type": "text/plain", "size": 128}, {"uid": "1a30372b214ed15d", "name": "test_completed", "source": "1a30372b214ed15d.png", "type": "image/png", "size": 593270}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": true}], "attachments": [{"uid": "2e8fb80dabb1405d", "name": "stdout", "source": "2e8fb80dabb1405d.txt", "type": "text/plain", "size": 13574}], "parameters": [], "attachmentsCount": 3, "shouldDisplayMessage": false, "stepsCount": 6, "attachmentStep": false, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753193196885, "stop": 1753193196885, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1753193196887, "stop": 1753193197042, "duration": 155}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1753193252010, "stop": 1753193252011, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "story", "value": "联系人控制命令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella"}, {"name": "suite", "value": "test_open_contact"}, {"name": "subSuite", "value": "TestEllaContactCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "53996-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.test_open_contact"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "3067993bcad541d8.json", "parameterValues": []}