{"uid": "7076375b905d4477", "name": "测试open contact命令 - 简洁版本", "fullName": "testcases.test_ella.test_open_app.TestEllaCommandConcise#test_open_app", "historyId": "a972283fef29b51b6464e93f4a297bde", "time": {"start": 1753193150076, "stop": 1753193162899, "duration": 12823}, "description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "descriptionHtml": "<p>使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753193144009, "stop": 1753193144382, "duration": 373}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": false}, {"name": "ella_app", "time": {"start": 1753193144383, "stop": 1753193150075, "duration": 5692}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1753193150075, "stop": 1753193150075, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": false}], "testStage": {"description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "status": "passed", "steps": [{"name": "执行命令: open app", "time": {"start": 1753193150076, "stop": 1753193162545, "duration": 12469}, "status": "passed", "steps": [{"name": "执行命令: open app", "time": {"start": 1753193150076, "stop": 1753193161651, "duration": 11575}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1753193161651, "stop": 1753193162544, "duration": 893}, "status": "passed", "steps": [], "attachments": [{"uid": "e177ce34c5fa0554", "name": "测试总结", "source": "e177ce34c5fa0554.txt", "type": "text/plain", "size": 379}, {"uid": "e917de118f2da8e6", "name": "test_completed", "source": "e917de118f2da8e6.png", "type": "image/png", "size": 569067}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "stepsCount": 2, "attachmentStep": false, "hasContent": true}, {"name": "验证响应包含Done", "time": {"start": 1753193162545, "stop": 1753193162548, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1753193162548, "stop": 1753193162898, "duration": 350}, "status": "passed", "steps": [], "attachments": [{"uid": "b75112d3a7ab151b", "name": "测试总结", "source": "b75112d3a7ab151b.txt", "type": "text/plain", "size": 379}, {"uid": "bf27ea6df600dfbd", "name": "test_completed", "source": "bf27ea6df600dfbd.png", "type": "image/png", "size": 569222}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": true}], "attachments": [{"uid": "fd4ab670d179e32b", "name": "stdout", "source": "fd4ab670d179e32b.txt", "type": "text/plain", "size": 11863}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "stepsCount": 5, "attachmentStep": false, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753193162900, "stop": 1753193162900, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1753193162903, "stop": 1753193163060, "duration": 157}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1753193252010, "stop": 1753193252011, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": false}], "labels": [{"name": "feature", "value": "设备信息"}, {"name": "story", "value": "设备型号: TECNO CM8"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "story", "value": "打开"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella"}, {"name": "suite", "value": "test_open_app"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "53996-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.test_open_app"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "7076375b905d4477.json", "parameterValues": []}