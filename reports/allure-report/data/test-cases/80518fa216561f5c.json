{"uid": "80518fa216561f5c", "name": "测试open contact命令 - 简洁版本", "fullName": "testcases.test_ella.test_open_ella.TestEllaCommandConcise#test_open_ella", "historyId": "4665de45505b728dffd3d02fb2efaea4", "time": {"start": 1753193202596, "stop": 1753193211376, "duration": 8780}, "description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "descriptionHtml": "<p>使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753193144009, "stop": 1753193144382, "duration": 373}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": false}, {"name": "ella_app", "time": {"start": 1753193197056, "stop": 1753193202594, "duration": 5538}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1753193202594, "stop": 1753193202594, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": false}], "testStage": {"description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "status": "passed", "steps": [{"name": "执行命令: open ella", "time": {"start": 1753193202597, "stop": 1753193211002, "duration": 8405}, "status": "passed", "steps": [{"name": "执行命令: open ella", "time": {"start": 1753193202597, "stop": 1753193210598, "duration": 8001}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1753193210598, "stop": 1753193211001, "duration": 403}, "status": "passed", "steps": [], "attachments": [{"uid": "77c96a1729b09854", "name": "测试总结", "source": "77c96a1729b09854.txt", "type": "text/plain", "size": 150}, {"uid": "30be4b42c338d9c9", "name": "test_completed", "source": "30be4b42c338d9c9.png", "type": "image/png", "size": 607215}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "stepsCount": 2, "attachmentStep": false, "hasContent": true}, {"name": "验证响应包含Done", "time": {"start": 1753193211002, "stop": 1753193211008, "duration": 6}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1753193211008, "stop": 1753193211376, "duration": 368}, "status": "passed", "steps": [], "attachments": [{"uid": "384df841d1d1ec00", "name": "测试总结", "source": "384df841d1d1ec00.txt", "type": "text/plain", "size": 150}, {"uid": "3cd8ebdb154dc9ba", "name": "test_completed", "source": "3cd8ebdb154dc9ba.png", "type": "image/png", "size": 607215}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": true}], "attachments": [{"uid": "d2cca9725750b5fe", "name": "stdout", "source": "d2cca9725750b5fe.txt", "type": "text/plain", "size": 9679}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "stepsCount": 5, "attachmentStep": false, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753193211378, "stop": 1753193211378, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1753193211380, "stop": 1753193211538, "duration": 158}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1753193252010, "stop": 1753193252011, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "story", "value": "打开"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella"}, {"name": "suite", "value": "test_open_ella"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "53996-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.test_open_ella"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "80518fa216561f5c.json", "parameterValues": []}