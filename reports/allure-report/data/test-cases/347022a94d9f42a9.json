{"uid": "347022a94d9f42a9", "name": "测试open contact命令 - 简洁版本", "fullName": "testcases.test_ella.test_open_bluetooth.TestEllaCommandConcise#test_open_bluetooth", "historyId": "e370b52aee9ba35516a7fb36220c8d53", "time": {"start": 1753193168740, "stop": 1753193181972, "duration": 13232}, "description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "descriptionHtml": "<p>使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753193144009, "stop": 1753193144382, "duration": 373}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": false}, {"name": "ella_app", "time": {"start": 1753193163068, "stop": 1753193168738, "duration": 5670}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1753193168739, "stop": 1753193168739, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": false}], "testStage": {"description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "status": "passed", "steps": [{"name": "执行命令: open bluetooth", "time": {"start": 1753193168740, "stop": 1753193181662, "duration": 12922}, "status": "passed", "steps": [{"name": "执行命令: open bluetooth", "time": {"start": 1753193168740, "stop": 1753193181336, "duration": 12596}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1753193181336, "stop": 1753193181661, "duration": 325}, "status": "passed", "steps": [], "attachments": [{"uid": "9d6099dfa4fc30a9", "name": "测试总结", "source": "9d6099dfa4fc30a9.txt", "type": "text/plain", "size": 191}, {"uid": "247f4903de0abaf2", "name": "test_completed", "source": "247f4903de0abaf2.png", "type": "image/png", "size": 542249}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "stepsCount": 2, "attachmentStep": false, "hasContent": true}, {"name": "验证响应包含Done", "time": {"start": 1753193181662, "stop": 1753193181669, "duration": 7}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": false}, {"name": "验证bluetooth已打开", "time": {"start": 1753193181669, "stop": 1753193181669, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1753193181669, "stop": 1753193181971, "duration": 302}, "status": "passed", "steps": [], "attachments": [{"uid": "88a0fd14495e0c0e", "name": "测试总结", "source": "88a0fd14495e0c0e.txt", "type": "text/plain", "size": 191}, {"uid": "592075033345f13b", "name": "test_completed", "source": "592075033345f13b.png", "type": "image/png", "size": 541428}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": true}], "attachments": [{"uid": "bdaeb3b9f0c8ff82", "name": "stdout", "source": "bdaeb3b9f0c8ff82.txt", "type": "text/plain", "size": 11290}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "stepsCount": 6, "attachmentStep": false, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753193181973, "stop": 1753193181973, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1753193181975, "stop": 1753193182162, "duration": 187}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1753193252010, "stop": 1753193252011, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": false}], "labels": [{"name": "story", "value": "联系人控制命令 - 简洁版本"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella"}, {"name": "suite", "value": "test_open_bluetooth"}, {"name": "subSuite", "value": "TestEllaCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "53996-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.test_open_bluetooth"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "347022a94d9f42a9.json", "parameterValues": []}