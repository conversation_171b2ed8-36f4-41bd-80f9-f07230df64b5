{"uid": "b455797dbc4b5f49", "name": "测试open contact命令", "fullName": "testcases.test_ella.test_open_phone.TestEllaContactCommandConcise#test_open_phone", "historyId": "93f6dcb971c765f4ba78d47e1e20e8f1", "time": {"start": 1753193217024, "stop": 1753193231002, "duration": 13978}, "description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "descriptionHtml": "<p>使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753193144009, "stop": 1753193144382, "duration": 373}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": false}, {"name": "ella_app", "time": {"start": 1753193211549, "stop": 1753193217020, "duration": 5471}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1753193217021, "stop": 1753193217021, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": false}], "testStage": {"description": "使用简化的测试框架测试联系人开启命令，验证响应包含Done且实际打开Dalier", "status": "passed", "steps": [{"name": "执行命令: open phone", "time": {"start": 1753193217024, "stop": 1753193230659, "duration": 13635}, "status": "passed", "steps": [{"name": "执行命令: open phone", "time": {"start": 1753193217024, "stop": 1753193230197, "duration": 13173}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1753193230197, "stop": 1753193230658, "duration": 461}, "status": "passed", "steps": [], "attachments": [{"uid": "788a2574b24d7668", "name": "测试总结", "source": "788a2574b24d7668.txt", "type": "text/plain", "size": 152}, {"uid": "7b73941a2038d65f", "name": "test_completed", "source": "7b73941a2038d65f.png", "type": "image/png", "size": 614687}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": true}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "stepsCount": 2, "attachmentStep": false, "hasContent": true}, {"name": "验证响应包含Done", "time": {"start": 1753193230659, "stop": 1753193230662, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": false}, {"name": "验证Dalier应用已打开", "time": {"start": 1753193230662, "stop": 1753193230662, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": false}, {"name": "记录测试结果", "time": {"start": 1753193230662, "stop": 1753193231001, "duration": 339}, "status": "passed", "steps": [], "attachments": [{"uid": "6aaef67ca6a1c06b", "name": "测试总结", "source": "6aaef67ca6a1c06b.txt", "type": "text/plain", "size": 152}, {"uid": "555bf54a6a1fb5df", "name": "test_completed", "source": "555bf54a6a1fb5df.png", "type": "image/png", "size": 614885}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": true}], "attachments": [{"uid": "d9aec2a90690ef93", "name": "stdout", "source": "d9aec2a90690ef93.txt", "type": "text/plain", "size": 13410}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "stepsCount": 6, "attachmentStep": false, "hasContent": true}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753193231003, "stop": 1753193231003, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": false}, {"name": "ella_app::0", "time": {"start": 1753193231005, "stop": 1753193231151, "duration": 146}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": false}, {"name": "setup_test_environment::0", "time": {"start": 1753193252010, "stop": 1753193252011, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "stepsCount": 0, "attachmentStep": false, "hasContent": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "critical"}, {"name": "story", "value": "联系人控制命令"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella"}, {"name": "suite", "value": "test_open_phone"}, {"name": "subSuite", "value": "TestEllaContactCommandConcise"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "53996-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.test_open_phone"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "b455797dbc4b5f49.json", "parameterValues": []}