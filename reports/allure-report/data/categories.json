{"uid": "4b4757e66a1912dae1a509f688f20b0f", "name": "categories", "children": [{"name": "Product defects", "children": [{"name": "AssertionError: 响应未包含期望内容: ['Sorry, weather data error,try later']\nassert False", "children": [{"name": "测试how's the weather today?返回正确的不支持响应", "uid": "a3eadc9fba00cf71", "parentUid": "03c9bd8bae4aa9c303fef1e05c77bb40", "status": "failed", "time": {"start": 1753765192515, "stop": 1753765205101, "duration": 12586}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "03c9bd8bae4aa9c303fef1e05c77bb40"}], "uid": "8fb3a91ba5aaf9de24cc8a92edc82b5d"}]}