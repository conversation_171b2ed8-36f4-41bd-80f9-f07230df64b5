"DESCRIPTION","DURATION IN MS","NAME","PARENT SUITE","START TIME","STATUS","STOP TIME","SUB SUITE","SUITE","TEST CLASS","TEST METHOD"
"验证close power saving mode指令返回预期的不支持响应","8673","测试close power saving mode返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 12:02:48 CST 2025","passed","Tue Jul 29 12:02:57 CST 2025","TestEllaClosePowerSavingMode","test_close_power_saving_mode","",""
"验证jump to battery usage指令返回预期的不支持响应","8739","测试jump to battery usage返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:02:48 CST 2025","passed","Tue Jul 29 13:02:57 CST 2025","TestEllaJumpBatteryUsage","test_jump_to_battery_usage","",""
"验证turn off show battery percentage指令返回预期的不支持响应","8508","测试turn off show battery percentage返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:23:29 CST 2025","passed","Tue Jul 29 13:23:38 CST 2025","TestEllaTurnOffShowBatteryPercentage","test_turn_off_show_battery_percentage","",""
"验证disable all ai magic box features指令返回预期的不支持响应","9146","测试disable all ai magic box features返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 12:03:32 CST 2025","passed","Tue Jul 29 12:03:42 CST 2025","TestEllaDisableAllAiMagicBoxFeatures","test_disable_all_ai_magic_box_features","",""
"验证set languages指令返回预期的不支持响应","8833","测试set languages返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:13:37 CST 2025","passed","Tue Jul 29 13:13:46 CST 2025","TestEllaSetLanguages","test_set_languages","",""
"验证disable brightness locking指令返回预期的不支持响应","8787","测试disable brightness locking返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 12:04:17 CST 2025","passed","Tue Jul 29 12:04:26 CST 2025","TestEllaDisableBrightnessLocking","test_disable_brightness_locking","",""
"验证jump to high brightness mode settings指令返回预期的不支持响应","12410","测试jump to high brightness mode settings返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:03:37 CST 2025","passed","Tue Jul 29 13:03:50 CST 2025","TestEllaJumpHighBrightnessModeSettings","test_jump_to_high_brightness_mode_settings","",""
"验证order a takeaway指令返回预期的不支持响应","8355","测试order a takeaway返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:06:37 CST 2025","passed","Tue Jul 29 13:06:45 CST 2025","TestEllaOrderTakeaway","test_order_a_takeaway","",""
"验证set date & time指令返回预期的不支持响应","8807","测试set date & time返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:10:33 CST 2025","passed","Tue Jul 29 13:10:42 CST 2025","TestEllaSetDateTime","test_set_date_time","",""
"验证set flex-still mode指令返回预期的不支持响应","8618","测试set flex-still mode返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:11:18 CST 2025","passed","Tue Jul 29 13:11:27 CST 2025","TestEllaSetFlexStillMode","test_set_flex_still_mode","",""
"验证Enable Call Rejection指令返回预期的不支持响应","12965","测试Enable Call Rejection返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 12:57:33 CST 2025","passed","Tue Jul 29 12:57:46 CST 2025","TestEllaEnableCallRejection","test_enable_call_rejection","",""
"验证set sim1 ringtone指令返回预期的不支持响应","8791","测试set sim1 ringtone返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:18:35 CST 2025","passed","Tue Jul 29 13:18:44 CST 2025","TestEllaSetSimRingtone","test_set_sim_ringtone","",""
"验证enable running lock指令返回预期的不支持响应","8282","测试enable running lock返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 12:58:22 CST 2025","passed","Tue Jul 29 12:58:31 CST 2025","TestEllaEnableRunningLock","test_enable_running_lock","",""
"验证disable unfreeze指令返回预期的不支持响应","9000","测试disable unfreeze返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 12:54:05 CST 2025","passed","Tue Jul 29 12:54:14 CST 2025","TestEllaDisableUnfreeze","test_disable_unfreeze","",""
"验证enable auto pickup指令返回预期的不支持响应","8957","测试enable auto pickup返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 12:56:22 CST 2025","passed","Tue Jul 29 12:56:31 CST 2025","TestEllaEnableAutoPickup","test_enable_auto_pickup","",""
"验证disable running lock指令返回预期的不支持响应","8427","测试disable running lock返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 12:53:21 CST 2025","passed","Tue Jul 29 12:53:30 CST 2025","TestEllaDisableRunningLock","test_disable_running_lock","",""
"验证set font size指令返回预期的不支持响应","9085","测试set font size返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:12:47 CST 2025","passed","Tue Jul 29 13:12:56 CST 2025","TestEllaSetFontSize","test_set_font_size","",""
"验证disable touch optimization指令返回预期的不支持响应","8553","测试disable touch optimization返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 12:53:43 CST 2025","passed","Tue Jul 29 12:53:51 CST 2025","TestEllaDisableTouchOptimization","test_disable_touch_optimization","",""
"验证jump to notifications and status bar settings指令返回预期的不支持响应","12475","测试jump to notifications and status bar settings返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:04:30 CST 2025","passed","Tue Jul 29 13:04:43 CST 2025","TestEllaJumpNotificationsStatusBarSettings","test_jump_to_notifications_and_status_bar_settings","",""
"验证disable hide notifications指令返回预期的不支持响应","8889","测试disable hide notifications返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 12:05:07 CST 2025","passed","Tue Jul 29 12:05:16 CST 2025","TestEllaDisableHideNotifications","test_disable_hide_notifications","",""
"验证turn on driving mode指令返回预期的不支持响应","8317","测试turn on driving mode返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:23:51 CST 2025","passed","Tue Jul 29 13:24:00 CST 2025","TestEllaTurnDrivingMode","test_turn_on_driving_mode","",""
"验证disable network enhancement指令返回预期的不支持响应","8644","测试disable network enhancement返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 12:52:59 CST 2025","passed","Tue Jul 29 12:53:08 CST 2025","TestEllaDisableNetworkEnhancement","test_disable_network_enhancement","",""
"验证set call back with last used sim指令返回预期的不支持响应","13014","测试set call back with last used sim返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:08:37 CST 2025","passed","Tue Jul 29 13:08:50 CST 2025","TestEllaSetCallBackLastUsedSim","test_set_call_back_with_last_used_sim","",""
"验证set customized cover screen指令返回预期的不支持响应","8388","测试set customized cover screen返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:10:11 CST 2025","passed","Tue Jul 29 13:10:19 CST 2025","TestEllaSetCustomizedCoverScreen","test_set_customized_cover_screen","",""
"验证Enable Network Enhancement指令返回预期的不支持响应","8258","测试Enable Network Enhancement返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 12:58:00 CST 2025","passed","Tue Jul 29 12:58:08 CST 2025","TestEllaEnableNetworkEnhancement","test_enable_network_enhancement","",""
"验证switch to power saving mode指令返回预期的不支持响应","8757","测试switch to power saving mode返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:21:59 CST 2025","passed","Tue Jul 29 13:22:08 CST 2025","TestEllaSwitchPowerSavingMode","test_switch_to_power_saving_mode","",""
"验证more settings指令返回预期的不支持响应","12166","测试more settings返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:04:56 CST 2025","passed","Tue Jul 29 13:05:08 CST 2025","TestEllaMoreSettings","test_more_settings","",""
"验证reset phone指令返回预期的不支持响应","13326","测试reset phone返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:06:58 CST 2025","passed","Tue Jul 29 13:07:12 CST 2025","TestEllaResetPhone","test_reset_phone","",""
"验证check mobile data balance of sim2指令返回预期的不支持响应","8918","测试check mobile data balance of sim2返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 12:00:55 CST 2025","passed","Tue Jul 29 12:01:04 CST 2025","TestEllaCheckMobileDataBalanceSim","test_check_mobile_data_balance_of_sim","",""
"验证set personal hotspot指令返回预期的不支持响应","9150","测试set personal hotspot返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:15:29 CST 2025","passed","Tue Jul 29 13:15:38 CST 2025","TestEllaSetPersonalHotspot","test_set_personal_hotspot","",""
"验证the second指令返回预期的不支持响应","8383","测试the second返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:22:44 CST 2025","passed","Tue Jul 29 13:22:52 CST 2025","TestEllaSecond","test_the_second","",""
"验证order a burger指令返回预期的不支持响应","8832","测试order a burger返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:06:14 CST 2025","passed","Tue Jul 29 13:06:23 CST 2025","TestEllaOrderBurger","test_order_a_burger","",""
"验证disable zonetouch master指令返回预期的不支持响应","8253","测试disable zonetouch master返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 12:54:27 CST 2025","passed","Tue Jul 29 12:54:35 CST 2025","TestEllaDisableZonetouchMaster","test_disable_zonetouch_master","",""
"验证set split-screen apps指令返回预期的不支持响应","9660","测试set split-screen apps返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:20:05 CST 2025","passed","Tue Jul 29 13:20:15 CST 2025","TestEllaSetSplitScreenApps","test_set_split_screen_apps","",""
"验证set parallel windows指令返回预期的不支持响应","8521","测试set parallel windows返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:15:07 CST 2025","passed","Tue Jul 29 13:15:15 CST 2025","TestEllaSetParallelWindows","test_set_parallel_windows","",""
"验证jump to auto rotate screen settings指令返回预期的不支持响应","12549","测试jump to auto rotate screen settings返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:01:59 CST 2025","passed","Tue Jul 29 13:02:12 CST 2025","TestEllaJumpAutoRotateScreenSettings","test_jump_to_auto_rotate_screen_settings","",""
"验证set phone number指令返回预期的不支持响应","13166","测试set phone number返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:16:15 CST 2025","passed","Tue Jul 29 13:16:28 CST 2025","TestEllaSetPhoneNumber","test_set_phone_number","",""
"验证set ultra power saving指令返回预期的不支持响应","9083","测试set ultra power saving返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:20:51 CST 2025","passed","Tue Jul 29 13:21:00 CST 2025","TestEllaSetUltraPowerSaving","test_set_ultra_power_saving","",""
"验证switch to equilibrium mode指令返回预期的不支持响应","9057","测试switch to equilibrium mode返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:21:14 CST 2025","passed","Tue Jul 29 13:21:23 CST 2025","TestEllaSwitchEquilibriumMode","test_switch_to_equilibrium_mode","",""
"验证set battery saver settings指令返回预期的不支持响应","12591","测试set battery saver settings返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:08:10 CST 2025","passed","Tue Jul 29 13:08:23 CST 2025","TestEllaSetBatterySaverSettings","test_set_battery_saver_settings","",""
"验证set smart panel指令返回预期的不支持响应","8566","测试set smart panel返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:19:20 CST 2025","passed","Tue Jul 29 13:19:29 CST 2025","TestEllaSetSmartPanel","test_set_smart_panel","",""
"验证check battery information指令返回预期的不支持响应","10016","测试check battery information返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 12:00:31 CST 2025","passed","Tue Jul 29 12:00:41 CST 2025","TestEllaCheckBatteryInformation","test_check_battery_information","",""
"验证set app auto rotate指令返回预期的不支持响应","9140","测试set app auto rotate返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:07:25 CST 2025","passed","Tue Jul 29 13:07:35 CST 2025","TestEllaSetAppAutoRotate","test_set_app_auto_rotate","",""
"验证download basketball指令返回预期的不支持响应","13574","测试download basketball返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 12:54:49 CST 2025","passed","Tue Jul 29 12:55:02 CST 2025","TestEllaDownloadBasketball","test_download_basketball","",""
"验证switching charging speed指令返回预期的不支持响应","8636","测试switching charging speed返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:22:22 CST 2025","passed","Tue Jul 29 13:22:30 CST 2025","TestEllaSwitchingChargingSpeed","test_switching_charging_speed","",""
"验证set screen to minimum brightness指令返回预期的不支持响应","8610","测试set screen to minimum brightness返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:18:12 CST 2025","passed","Tue Jul 29 13:18:21 CST 2025","TestEllaSetScreenMinimumBrightness","test_set_screen_to_minimum_brightness","",""
"验证jump to adaptive brightness settings指令返回预期的不支持响应","12548","测试jump to adaptive brightness settings返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:01:07 CST 2025","passed","Tue Jul 29 13:01:20 CST 2025","TestEllaJumpAdaptiveBrightnessSettings","test_jump_to_adaptive_brightness_settings","",""
"验证disable magic voice changer指令返回预期的不支持响应","7784","测试disable magic voice changer返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 12:52:38 CST 2025","passed","Tue Jul 29 12:52:45 CST 2025","TestEllaDisableMagicVoiceChanger","test_disable_magic_voice_changer","",""
"验证set smart hub指令返回预期的不支持响应","8770","测试set smart hub返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:18:57 CST 2025","passed","Tue Jul 29 13:19:06 CST 2025","TestEllaSetSmartHub","test_set_smart_hub","",""
"验证how's the weather today?指令返回预期的不支持响应","12586","测试how's the weather today?返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 12:59:52 CST 2025","failed","Tue Jul 29 13:00:05 CST 2025","TestEllaHowSWeatherToday","test_how_s_the_weather_today","",""
"验证turn on high brightness mode指令返回预期的不支持响应","8824","测试turn on high brightness mode返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:24:14 CST 2025","passed","Tue Jul 29 13:24:22 CST 2025","TestEllaTurnHighBrightnessMode","test_turn_on_high_brightness_mode","",""
"验证set color style指令返回预期的不支持响应","8588","测试set color style返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:09:04 CST 2025","passed","Tue Jul 29 13:09:12 CST 2025","TestEllaSetColorStyle","test_set_color_style","",""
"验证Voice setting page指令返回预期的不支持响应","12809","测试Voice setting page返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:25:00 CST 2025","passed","Tue Jul 29 13:25:12 CST 2025","TestEllaVoiceSettingPage","test_voice_setting_page","",""
"验证set phantom v pen指令返回预期的不支持响应","8818","测试set phantom v pen返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:15:52 CST 2025","passed","Tue Jul 29 13:16:01 CST 2025","TestEllaSetPhantomVPen","test_set_phantom_v_pen","",""
"验证set special function指令返回预期的不支持响应","8554","测试set special function返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:19:42 CST 2025","passed","Tue Jul 29 13:19:51 CST 2025","TestEllaSetSpecialFunction","test_set_special_function","",""
"验证open notification ringtone settings指令返回预期的不支持响应","12339","测试open notification ringtone settings返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:05:48 CST 2025","passed","Tue Jul 29 13:06:00 CST 2025","TestEllaOpenSettings","test_open_notification_ringtone_settings","",""
"验证enable unfreeze指令返回预期的不支持响应","8406","测试enable unfreeze返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 12:59:07 CST 2025","passed","Tue Jul 29 12:59:15 CST 2025","TestEllaEnableUnfreeze","test_enable_unfreeze","",""
"验证set screen relay指令返回预期的不支持响应","9146","测试set screen relay返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:17:27 CST 2025","passed","Tue Jul 29 13:17:36 CST 2025","TestEllaSetScreenRelay","test_set_screen_relay","",""
"验证jump to ai wallpaper generator settings指令返回预期的不支持响应","12203","测试jump to ai wallpaper generator settings返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:01:33 CST 2025","passed","Tue Jul 29 13:01:45 CST 2025","TestEllaJumpAiWallpaperGeneratorSettings","test_jump_to_ai_wallpaper_generator_settings","",""
"验证jump to call notifications指令返回预期的不支持响应","12907","测试jump to call notifications返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:03:10 CST 2025","passed","Tue Jul 29 13:03:23 CST 2025","TestEllaJumpCallNotifications","test_jump_to_call_notifications","",""
"验证check my balance of sim1指令返回预期的不支持响应","9751","测试check my balance of sim1返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 12:01:40 CST 2025","passed","Tue Jul 29 12:01:49 CST 2025","TestEllaCheckMyBalanceSim","test_check_my_balance_of_sim","",""
"验证close equilibrium mode指令返回预期的不支持响应","9278","测试close equilibrium mode返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 12:02:03 CST 2025","passed","Tue Jul 29 12:02:13 CST 2025","TestEllaCloseEquilibriumMode","test_close_equilibrium_mode","",""
"验证set my themes指令返回预期的不支持响应","8457","测试set my themes返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:14:45 CST 2025","passed","Tue Jul 29 13:14:53 CST 2025","TestEllaSetMyThemes","test_set_my_themes","",""
"验证set timezone指令返回预期的不支持响应","8417","测试set timezone返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:20:28 CST 2025","passed","Tue Jul 29 13:20:37 CST 2025","TestEllaSetTimezone","test_set_timezone","",""
"验证disable accelerate dialogue指令返回预期的不支持响应","8769","测试disable accelerate dialogue返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 12:03:10 CST 2025","passed","Tue Jul 29 12:03:19 CST 2025","TestEllaDisableAccelerateDialogue","test_disable_accelerate_dialogue","",""
"验证how to set screenshots指令返回预期的不支持响应","8502","测试how to set screenshots返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:00:19 CST 2025","passed","Tue Jul 29 13:00:27 CST 2025","TestEllaHowSetScreenshots","test_how_to_set_screenshots","",""
"验证turn on show battery percentage指令返回预期的不支持响应","9094","测试turn on show battery percentage返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:24:36 CST 2025","passed","Tue Jul 29 13:24:45 CST 2025","TestEllaTurnShowBatteryPercentage","test_turn_on_show_battery_percentage","",""
"验证jump to lock screen notification and display settings指令返回预期的不支持响应","12814","测试jump to lock screen notification and display settings返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:04:03 CST 2025","passed","Tue Jul 29 13:04:16 CST 2025","TestEllaOpenSettings","test_jump_to_lock_screen_notification_and_display_settings","",""
"验证enable brightness locking指令返回预期的不支持响应","8273","测试enable brightness locking返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 12:56:44 CST 2025","passed","Tue Jul 29 12:56:52 CST 2025","TestEllaEnableBrightnessLocking","test_enable_brightness_locking","",""
"验证set lockscreen passwords指令返回预期的不支持响应","8624","测试set lockscreen passwords返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:14:00 CST 2025","passed","Tue Jul 29 13:14:08 CST 2025","TestEllaSetLockscreenPasswords","test_set_lockscreen_passwords","",""
"验证enable accelerate dialogue指令返回预期的不支持响应","9012","测试enable accelerate dialogue返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 12:55:37 CST 2025","passed","Tue Jul 29 12:55:46 CST 2025","TestEllaEnableAccelerateDialogue","test_enable_accelerate_dialogue","",""
"验证set flip case feature指令返回预期的不支持响应","8254","测试set flip case feature返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:11:41 CST 2025","passed","Tue Jul 29 13:11:49 CST 2025","TestEllaSetFlipCaseFeature","test_set_flip_case_feature","",""
"验证set gesture navigation指令返回预期的不支持响应","12841","测试set gesture navigation返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:13:10 CST 2025","passed","Tue Jul 29 13:13:23 CST 2025","TestEllaSetGestureNavigation","test_set_gesture_navigation","",""
"验证set screen timeout指令返回预期的不支持响应","8672","测试set screen timeout返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:17:50 CST 2025","passed","Tue Jul 29 13:17:59 CST 2025","TestEllaSetScreenTimeout","test_set_screen_timeout","",""
"验证set floating windows指令返回预期的不支持响应","8660","测试set floating windows返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:12:03 CST 2025","passed","Tue Jul 29 13:12:11 CST 2025","TestEllaSetFloatingWindows","test_set_floating_windows","",""
"验证set screen refresh rate指令返回预期的不支持响应","8403","测试set screen refresh rate返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:17:04 CST 2025","passed","Tue Jul 29 13:17:13 CST 2025","TestEllaSetScreenRefreshRate","test_set_screen_refresh_rate","",""
"验证set scheduled power on/off and restart指令返回预期的不支持响应","9102","测试set scheduled power on/off and restart返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:16:42 CST 2025","passed","Tue Jul 29 13:16:51 CST 2025","TestEllaSetScheduledPowerOffRestart","test_set_scheduled_power_on_off_and_restart","",""
"验证close performance mode指令返回预期的不支持响应","8918","测试close performance mode返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 12:02:26 CST 2025","passed","Tue Jul 29 12:02:35 CST 2025","TestEllaClosePerformanceMode","test_close_performance_mode","",""
"验证driving mode指令返回预期的不支持响应","8766","测试driving mode返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 12:55:15 CST 2025","passed","Tue Jul 29 12:55:24 CST 2025","TestEllaDrivingMode","test_driving_mode","",""
"验证disable call rejection指令返回预期的不支持响应","13323","测试disable call rejection返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 12:04:40 CST 2025","passed","Tue Jul 29 12:04:53 CST 2025","TestEllaDisableCallRejection","test_disable_call_rejection","",""
"验证check model information指令返回预期的不支持响应","9005","测试check model information返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 12:01:17 CST 2025","passed","Tue Jul 29 12:01:26 CST 2025","TestEllaCheckModelInformation","test_check_model_information","",""
"验证Enable Call on Hold指令返回预期的不支持响应","12941","测试Enable Call on Hold返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 12:57:06 CST 2025","passed","Tue Jul 29 12:57:19 CST 2025","TestEllaEnableCallHold","test_enable_call_on_hold","",""
"验证enable touch optimization指令返回预期的不支持响应","8708","测试enable touch optimization返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 12:58:45 CST 2025","passed","Tue Jul 29 12:58:53 CST 2025","TestEllaEnableTouchOptimization","test_enable_touch_optimization","",""
"验证disable auto pickup指令返回预期的不支持响应","8424","测试disable auto pickup返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 12:03:55 CST 2025","passed","Tue Jul 29 12:04:04 CST 2025","TestEllaDisableAutoPickup","test_disable_auto_pickup","",""
"验证enable zonetouch master指令返回预期的不支持响应","8800","测试enable zonetouch master返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 12:59:30 CST 2025","passed","Tue Jul 29 12:59:38 CST 2025","TestEllaEnableZonetouchMaster","test_enable_zonetouch_master","",""
"验证increase settings for special functions指令返回预期的不支持响应","12040","测试increase settings for special functions返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:00:41 CST 2025","passed","Tue Jul 29 13:00:53 CST 2025","TestEllaIncreaseSettingsSpecialFunctions","test_increase_settings_for_special_functions","",""
"验证jump to battery and power saving指令返回预期的不支持响应","8897","测试jump to battery and power saving返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:02:25 CST 2025","passed","Tue Jul 29 13:02:34 CST 2025","TestEllaJumpBatteryPowerSaving","test_jump_to_battery_and_power_saving","",""
"验证yandex eats指令返回预期的不支持响应","8602","测试yandex eats返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:25:27 CST 2025","passed","Tue Jul 29 13:25:36 CST 2025","TestEllaYandexEats","test_yandex_eats","",""
"验证set app notifications指令返回预期的不支持响应","8917","测试set app notifications返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:07:48 CST 2025","passed","Tue Jul 29 13:07:57 CST 2025","TestEllaSetAppNotifications","test_set_app_notifications","",""
"验证set folding screen zone指令返回预期的不支持响应","8480","测试set folding screen zone返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:12:25 CST 2025","passed","Tue Jul 29 13:12:33 CST 2025","TestEllaSetFoldingScreenZone","test_set_folding_screen_zone","",""
"验证open font family settings指令返回预期的不支持响应","12128","测试open font family settings返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:05:22 CST 2025","passed","Tue Jul 29 13:05:34 CST 2025","TestEllaOpenSettings","test_open_font_family_settings","",""
"验证set edge mistouch prevention指令返回预期的不支持响应","8558","测试set edge mistouch prevention返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:10:56 CST 2025","passed","Tue Jul 29 13:11:04 CST 2025","TestEllaSetEdgeMistouchPrevention","test_set_edge_mistouch_prevention","",""
"验证set my fonts指令返回预期的不支持响应","8797","测试set my fonts返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:14:22 CST 2025","passed","Tue Jul 29 13:14:31 CST 2025","TestEllaSetMyFonts","test_set_my_fonts","",""
"验证set cover screen apps指令返回预期的不支持响应","8273","测试set cover screen apps返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:09:49 CST 2025","passed","Tue Jul 29 13:09:57 CST 2025","TestEllaSetCoverScreenApps","test_set_cover_screen_apps","",""
"验证switch to performance mode指令返回预期的不支持响应","8257","测试switch to performance mode返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:21:37 CST 2025","passed","Tue Jul 29 13:21:45 CST 2025","TestEllaSwitchPerformanceMode","test_switch_to_performance_mode","",""
"验证turn off driving mode指令返回预期的不支持响应","8900","测试turn off driving mode返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:23:06 CST 2025","passed","Tue Jul 29 13:23:15 CST 2025","TestEllaTurnOffDrivingMode","test_turn_off_driving_mode","",""
"验证enable all ai magic box features指令返回预期的不支持响应","9000","测试enable all ai magic box features返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 12:55:59 CST 2025","passed","Tue Jul 29 12:56:08 CST 2025","TestEllaEnableAllAiMagicBoxFeatures","test_enable_all_ai_magic_box_features","",""
"验证set compatibility mode指令返回预期的不支持响应","8755","测试set compatibility mode返回正确的不支持响应","testcases.test_ella.unsupported_commands","Tue Jul 29 13:09:27 CST 2025","passed","Tue Jul 29 13:09:35 CST 2025","TestEllaSetCompatibilityMode","test_set_compatibility_mode","",""
