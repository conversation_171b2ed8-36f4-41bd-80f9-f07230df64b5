2025-07-22 22:05:44 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-22 22:05:44 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-22 22:05:44 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-22 22:05:44 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-22 22:05:44 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-22 22:05:44 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-22 22:05:44 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:05:47 | INFO | pages.apps.ella.ella_status_checker:check_app_started:335 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-22 22:05:47 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-22 22:05:47 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-22 22:05:47 | INFO | pages.apps.ella.ella_status_checker:check_service_health:292 | 检查UIAutomator2服务健康状态
2025-07-22 22:05:48 | INFO | pages.apps.ella.ella_status_checker:check_service_health:308 | ✅ UIAutomator2服务健康状态良好
2025-07-22 22:05:48 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-22 22:05:48 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-22 22:05:48 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-22 22:05:49 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-22 22:05:50 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 22:05:50 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open app，状态: None
2025-07-22 22:05:50 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 22:05:50 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 22:05:50 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:05:50 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:05:50 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 22:05:50 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 22:05:50 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 22:05:50 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 22:05:50 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open app
2025-07-22 22:05:50 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 22:05:50 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 22:05:50 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open app
2025-07-22 22:05:50 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 22:05:50 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:05:50 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 22:05:51 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-22 22:05:51 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 22:05:51 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:05:51 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 22:05:51 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open app
2025-07-22 22:05:51 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-22 22:05:51 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-22 22:05:52 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-22 22:05:52 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:05:52 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-22 22:05:53 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-22 22:05:53 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-22 22:05:53 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-22 22:05:53 | INFO | testcases.test_ella.base_ella_test:_execute_command:347 | ✅ 成功执行命令: open app
2025-07-22 22:05:53 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-22 22:05:53 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:58 | ✅ 通过TTS按钮检测到响应
2025-07-22 22:05:53 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:90 | 状态检查完成，现在获取响应文本
2025-07-22 22:05:53 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第1次尝试确保在Ella页面以获取响应
2025-07-22 22:05:53 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 22:05:53 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 22:05:54 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:05:54 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:05:54 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 22:05:54 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 22:05:54 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:98 | ✅ 已确认在Ella对话页面，可以获取响应
2025-07-22 22:05:54 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 22:05:54 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:05:54 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:05:54 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 22:05:54 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:129 | 获取AI响应文本
2025-07-22 22:05:56 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:290 | asr_txt文本不符合AI响应格式: open app，已达到最大重试次数
2025-07-22 22:05:57 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:251 | robot_text节点不存在，已达到最大重试次数
2025-07-22 22:05:58 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:251 | function_name节点不存在，已达到最大重试次数
2025-07-22 22:05:59 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:251 | function_control节点不存在，已达到最大重试次数
2025-07-22 22:05:59 | WARNING | pages.apps.ella.ella_response_handler:get_response_all_text:143 | 尝试获取其他有效的响应文本
2025-07-22 22:05:59 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_text_views:539 | 从TextView元素获取响应
2025-07-22 22:06:01 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_chat_list:555 | 查找RecyclerView中的最新消息
2025-07-22 22:06:01 | INFO | pages.apps.ella.ella_response_handler:_extract_text_from_check_area_dump:387 | 从dump正则提取文本: Dialogue Explore Refresh AstraZeneca's $50B US Expansion Amid Tariffs Kings Boost Depth with Draft Picks, Schroder What is Ask About Screen? open app Which app should I open? YouTube Instagram Calendar DeepSeek-R1 Please enter 22:06
2025-07-22 22:06:01 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:155 | ✅ 获取到响应文本: Dialogue Explore Refresh AstraZeneca's $50B US Expansion Amid Tariffs Kings Boost Depth with Draft Picks, Schroder What is Ask About Screen? open app Which app should I open? YouTube Instagram Calendar DeepSeek-R1 Please enter 22:06
2025-07-22 22:06:01 | WARNING | pages.apps.ella.ella_response_handler:get_response_all_text:160 | 未获取到有效的响应文本
2025-07-22 22:06:01 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:119 | 最终获取的AI响应: '['open app', '', '', '', "Dialogue Explore Refresh AstraZeneca's $50B US Expansion Amid Tariffs Kings Boost Depth with Draft Picks, Schroder What is Ask About Screen? open app Which app should I open? YouTube Instagram Calendar DeepSeek-R1 Please enter 22:06"]'
2025-07-22 22:06:01 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:447 | ✅ 状态验证通过: None -> None
2025-07-22 22:06:02 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-22 22:06:02 | INFO | testcases.test_ella.base_ella_test:simple_command_test:672 | 🎉 open app 测试完成
2025-07-22 22:06:02 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:484 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open app', '', '', '', "Dialogue Explore Refresh AstraZeneca's $50B US Expansion Amid Tariffs Kings Boost Depth with Draft Picks, Schroder What is Ask About Screen? open app Which app should I open? YouTube Instagram Calendar DeepSeek-R1 Please enter 22:06"]
2025-07-22 22:06:02 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:528 | ✅ 响应包含期望内容: 'which app should i open'
2025-07-22 22:06:02 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:536 | 🎉 所有期望内容都已找到 (1/1)
2025-07-22 22:06:02 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-22 22:06:02 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-22 22:06:02 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
