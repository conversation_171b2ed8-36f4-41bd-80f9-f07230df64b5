2025-07-22 22:06:22 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-22 22:06:22 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-22 22:06:22 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:06:25 | INFO | pages.apps.ella.ella_status_checker:check_app_started:335 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-22 22:06:25 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-22 22:06:25 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-22 22:06:25 | INFO | pages.apps.ella.ella_status_checker:check_service_health:292 | 检查UIAutomator2服务健康状态
2025-07-22 22:06:26 | INFO | pages.apps.ella.ella_status_checker:check_service_health:308 | ✅ UIAutomator2服务健康状态良好
2025-07-22 22:06:26 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-22 22:06:26 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-22 22:06:26 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-22 22:06:26 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-22 22:06:27 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-22 22:06:27 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:122 | 检查联系人应用状态
2025-07-22 22:06:27 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:136 | === 当前活动应用信息（前500字符）===
2025-07-22 22:06:27 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:137 | ACTIVITY MANAGER ACTIVITIES (dumpsys activity activities)
Display #0 (activities from top to bottom):
  * Task{6f57a88 #116 type=standard A=1000:com.transsion.aivoiceassistant U=0 visible=true visibleRequested=true mode=fullscreen translucent=false sz=1}
    mLastNonFullscreenBounds=Rect(279, 699 - 801, 1779)
    isSleeping=false
    topResumedActivity=ActivityRecord{9142d96 u0 com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity t116}
    * Hist  #0
2025-07-22 22:06:27 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:147 | ✅ 检测到联系人应用: com.sh.smart.caller
2025-07-22 22:06:27 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:35 | 初始状态 - Dalier应用打开: True
2025-07-22 22:06:27 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 22:06:27 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 22:06:28 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:06:28 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:06:28 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 22:06:28 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 22:06:28 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 22:06:28 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 22:06:28 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open contact
2025-07-22 22:06:28 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-22 22:06:28 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-22 22:06:28 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open contact
2025-07-22 22:06:28 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 22:06:28 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:06:28 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 22:06:28 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-22 22:06:29 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-22 22:06:29 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:06:29 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-22 22:06:29 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open contact
2025-07-22 22:06:29 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-22 22:06:29 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-22 22:06:29 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-22 22:06:29 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-22 22:06:29 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-22 22:06:29 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-22 22:06:29 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-22 22:06:29 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-22 22:06:29 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:44 | ✅ 成功执行命令: open contact
2025-07-22 22:06:32 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:50 | 检查执行命令后的当前页面状态...
2025-07-22 22:06:32 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 22:06:32 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 22:06:33 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.sh.smart.caller
2025-07-22 22:06:33 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.android.dialer.main.impl.MainActivity
2025-07-22 22:06:33 | WARNING | pages.apps.ella.ella_status_checker:ensure_ella_process:273 | ❌ 当前不在Ella应用进程，当前包名: com.sh.smart.caller
2025-07-22 22:06:33 | WARNING | pages.apps.ella.main_page_refactored:ensure_on_chat_page:257 | 当前不在Ella进程，尝试返回Ella
2025-07-22 22:06:33 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:316 | 尝试返回Ella应用...
2025-07-22 22:06:33 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:320 | 第1次按返回键...
2025-07-22 22:06:34 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 22:06:34 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:06:34 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:06:34 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 22:06:34 | INFO | pages.apps.ella.main_page_refactored:return_to_ella_app:325 | ✅ 通过返回键回到Ella应用 (第1次)
2025-07-22 22:06:35 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 22:06:35 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:65 | ✅ 当前仍在Ella页面
2025-07-22 22:06:35 | INFO | pages.apps.ella.ella_contact_command_handler:_get_response_with_retry:89 | 尝试获取响应文本 (第1次)
2025-07-22 22:06:35 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-22 22:06:35 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 22:06:35 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:06:35 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:06:35 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 22:06:35 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-22 22:06:35 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 22:06:35 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:06:35 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:06:35 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 22:06:35 | INFO | pages.apps.ella.ella_response_handler:get_response_text:83 | 获取AI响应文本
2025-07-22 22:06:35 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_check_area:318 | 从check_area节点获取响应文本
2025-07-22 22:06:36 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_check_area:330 | 从check_area直接获取文本: Done!
2025-07-22 22:06:36 | INFO | pages.apps.ella.ella_response_handler:get_response_text:88 | ✅ 从check_area获取到响应文本: Done!
2025-07-22 22:06:36 | INFO | pages.apps.ella.ella_contact_command_handler:_get_response_with_retry:104 | ✅ 成功获取响应文本: Done!
2025-07-22 22:06:36 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:71 | 检查最终状态 - Dalier应用是否已打开
2025-07-22 22:06:36 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:253 | 检查当前进程是否是Ella...
2025-07-22 22:06:36 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:260 | 当前应用: com.transsion.aivoiceassistant
2025-07-22 22:06:36 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:261 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-22 22:06:36 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:270 | ✅ 当前在Ella应用进程
2025-07-22 22:06:36 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:122 | 检查联系人应用状态
2025-07-22 22:06:36 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:136 | === 当前活动应用信息（前500字符）===
2025-07-22 22:06:36 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:137 | ACTIVITY MANAGER ACTIVITIES (dumpsys activity activities)
Display #0 (activities from top to bottom):
  * Task{6f57a88 #116 type=standard A=1000:com.transsion.aivoiceassistant U=0 visible=true visibleRequested=true mode=fullscreen translucent=false sz=1}
    mLastPausedActivity: ActivityRecord{9142d96 u0 com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity t116}
    mLastNonFullscreenBounds=Rect(279, 699 - 801, 1779)
    isSleeping=false
    topResum
2025-07-22 22:06:36 | INFO | pages.apps.ella.ella_app_detector:check_contacts_app_opened:147 | ✅ 检测到联系人应用: com.sh.smart.caller
2025-07-22 22:06:36 | INFO | pages.apps.ella.ella_contact_command_handler:execute_contact_command_with_retry:74 | 命令执行完成: 初始状态=True, 最终状态=True, 响应='Done!'
2025-07-22 22:06:36 | INFO | pages.apps.ella.ella_contact_command_handler:verify_contact_command_result:209 | 应用状态检查结果: 初始=True, 最终=True
2025-07-22 22:06:36 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:484 | verify_expected_in_response 响应类型: <class 'str'>, 内容: Done!
2025-07-22 22:06:36 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:528 | ✅ 响应包含期望内容: 'done'
2025-07-22 22:06:36 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:536 | 🎉 所有期望内容都已找到 (1/1)
2025-07-22 22:06:36 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaContactCommandConcise\test_completed.png
2025-07-22 22:06:36 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-22 22:06:36 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
