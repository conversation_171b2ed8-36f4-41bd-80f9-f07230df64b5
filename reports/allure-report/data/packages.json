{"uid": "83edc06c07f9ae9e47eb6dd1b683e4e2", "name": "packages", "children": [{"name": "testcases.test_ella.unsupported_commands", "children": [{"name": "test_check_battery_information", "children": [{"name": "测试check battery information返回正确的不支持响应", "uid": "b766f5a20f897743", "parentUid": "8757fd926e61f6ebb245d3d3d474c927", "status": "passed", "time": {"start": 1753761631982, "stop": 1753761641998, "duration": 10016}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8757fd926e61f6ebb245d3d3d474c927"}, {"name": "test_check_mobile_data_balance_of_sim", "children": [{"name": "测试check mobile data balance of sim2返回正确的不支持响应", "uid": "2a28697021ec7d75", "parentUid": "837efc09915a750bd95f96f1811eb69d", "status": "passed", "time": {"start": 1753761655231, "stop": 1753761664149, "duration": 8918}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "837efc09915a750bd95f96f1811eb69d"}, {"name": "test_check_model_information", "children": [{"name": "测试check model information返回正确的不支持响应", "uid": "a56d8e321dbdf739", "parentUid": "78831bedd8240ff30a47ed4c298163e6", "status": "passed", "time": {"start": 1753761677631, "stop": 1753761686636, "duration": 9005}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "78831bedd8240ff30a47ed4c298163e6"}, {"name": "test_check_my_balance_of_sim", "children": [{"name": "测试check my balance of sim1返回正确的不支持响应", "uid": "676b5a85a463de98", "parentUid": "bddf49f414a6afcdd8f3aa88c2990b73", "status": "passed", "time": {"start": 1753761700135, "stop": 1753761709886, "duration": 9751}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bddf49f414a6afcdd8f3aa88c2990b73"}, {"name": "test_close_equilibrium_mode", "children": [{"name": "测试close equilibrium mode返回正确的不支持响应", "uid": "977a0552709ca7dd", "parentUid": "2a21ec0aa1162e7205ed4479032eec28", "status": "passed", "time": {"start": 1753761723925, "stop": 1753761733203, "duration": 9278}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2a21ec0aa1162e7205ed4479032eec28"}, {"name": "test_close_performance_mode", "children": [{"name": "测试close performance mode返回正确的不支持响应", "uid": "f5a5bd26d1bd1074", "parentUid": "95753a1df5aed1e2d1f53aaac547d09e", "status": "passed", "time": {"start": 1753761746394, "stop": 1753761755312, "duration": 8918}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "95753a1df5aed1e2d1f53aaac547d09e"}, {"name": "test_close_power_saving_mode", "children": [{"name": "测试close power saving mode返回正确的不支持响应", "uid": "8383bffd0b084967", "parentUid": "cc7047e4d72a0d0f3090e6371b0aadaf", "status": "passed", "time": {"start": 1753761768665, "stop": 1753761777338, "duration": 8673}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cc7047e4d72a0d0f3090e6371b0aadaf"}, {"name": "test_disable_accelerate_dialogue", "children": [{"name": "测试disable accelerate dialogue返回正确的不支持响应", "uid": "e3bf02bacdd2495b", "parentUid": "eb416fd31892d7985df8f4a00fa61281", "status": "passed", "time": {"start": 1753761790803, "stop": 1753761799572, "duration": 8769}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "eb416fd31892d7985df8f4a00fa61281"}, {"name": "test_disable_all_ai_magic_box_features", "children": [{"name": "测试disable all ai magic box features返回正确的不支持响应", "uid": "7d7e356db129b7a7", "parentUid": "ae74f6051eb946be44a497a5345a6122", "status": "passed", "time": {"start": 1753761812917, "stop": 1753761822063, "duration": 9146}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ae74f6051eb946be44a497a5345a6122"}, {"name": "test_disable_auto_pickup", "children": [{"name": "测试disable auto pickup返回正确的不支持响应", "uid": "c2c1ab04ba6a67df", "parentUid": "12db62453bb023c9292784bafa6f3501", "status": "passed", "time": {"start": 1753761835778, "stop": 1753761844202, "duration": 8424}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "12db62453bb023c9292784bafa6f3501"}, {"name": "test_disable_brightness_locking", "children": [{"name": "测试disable brightness locking返回正确的不支持响应", "uid": "ab574fd223ac3ccd", "parentUid": "3dcacd9d4311d09538f3bfd9137d3407", "status": "passed", "time": {"start": 1753761857701, "stop": 1753761866488, "duration": 8787}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3dcacd9d4311d09538f3bfd9137d3407"}, {"name": "test_disable_call_rejection", "children": [{"name": "测试disable call rejection返回正确的不支持响应", "uid": "cb7dced3ed5dce81", "parentUid": "2df8406295adf2f3ca8780a6fd181bd8", "status": "passed", "time": {"start": 1753761880191, "stop": 1753761893514, "duration": 13323}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2df8406295adf2f3ca8780a6fd181bd8"}, {"name": "test_disable_hide_notifications", "children": [{"name": "测试disable hide notifications返回正确的不支持响应", "uid": "c2492372c4391305", "parentUid": "4f012dc2e8bb211fd5ead2f1f23cd42d", "status": "passed", "time": {"start": 1753761907303, "stop": 1753761916192, "duration": 8889}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4f012dc2e8bb211fd5ead2f1f23cd42d"}, {"name": "test_disable_magic_voice_changer", "children": [{"name": "测试disable magic voice changer返回正确的不支持响应", "uid": "2184d35880254919", "parentUid": "a6c9b5ce2532611959a8b1cf0b7abdf0", "status": "passed", "time": {"start": 1753764758200, "stop": 1753764765984, "duration": 7784}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a6c9b5ce2532611959a8b1cf0b7abdf0"}, {"name": "test_disable_network_enhancement", "children": [{"name": "测试disable network enhancement返回正确的不支持响应", "uid": "7d3b9bd829e39087", "parentUid": "4c36c5378c3644d7cdbf63c08440a304", "status": "passed", "time": {"start": 1753764779947, "stop": 1753764788591, "duration": 8644}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4c36c5378c3644d7cdbf63c08440a304"}, {"name": "test_disable_running_lock", "children": [{"name": "测试disable running lock返回正确的不支持响应", "uid": "d872f1ccc0009a0b", "parentUid": "bdd8ac18c65aee858c82eb5de9f7b502", "status": "passed", "time": {"start": 1753764801669, "stop": 1753764810096, "duration": 8427}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bdd8ac18c65aee858c82eb5de9f7b502"}, {"name": "test_disable_touch_optimization", "children": [{"name": "测试disable touch optimization返回正确的不支持响应", "uid": "73d90eb159607760", "parentUid": "7777d1894ae8ebdcc0703d8d7a1d888d", "status": "passed", "time": {"start": 1753764823176, "stop": 1753764831729, "duration": 8553}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7777d1894ae8ebdcc0703d8d7a1d888d"}, {"name": "test_disable_unfreeze", "children": [{"name": "测试disable unfreeze返回正确的不支持响应", "uid": "771aecbdf39fcdf1", "parentUid": "f4f38e300a9eadad2128e98bc9820a33", "status": "passed", "time": {"start": 1753764845156, "stop": 1753764854156, "duration": 9000}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f4f38e300a9eadad2128e98bc9820a33"}, {"name": "test_disable_zonetouch_master", "children": [{"name": "测试disable zonetouch master返回正确的不支持响应", "uid": "865b70c233569c0b", "parentUid": "b2b3f74c91ec8af12a7b70a1858cb09f", "status": "passed", "time": {"start": 1753764867517, "stop": 1753764875770, "duration": 8253}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b2b3f74c91ec8af12a7b70a1858cb09f"}, {"name": "test_download_basketball", "children": [{"name": "测试download basketball返回正确的不支持响应", "uid": "888dd68c71948402", "parentUid": "ac5ff5b664434d007d90715847bb0efa", "status": "passed", "time": {"start": 1753764889004, "stop": 1753764902578, "duration": 13574}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ac5ff5b664434d007d90715847bb0efa"}, {"name": "test_driving_mode", "children": [{"name": "测试driving mode返回正确的不支持响应", "uid": "f8e5b04c258276eb", "parentUid": "e021bf6bc9463d8e3bd9aab9475fb30a", "status": "passed", "time": {"start": 1753764915935, "stop": 1753764924701, "duration": 8766}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e021bf6bc9463d8e3bd9aab9475fb30a"}, {"name": "test_enable_accelerate_dialogue", "children": [{"name": "测试enable accelerate dialogue返回正确的不支持响应", "uid": "eacf804ad7a8f2d5", "parentUid": "29814f9b6b5bad3852e86d186b662dab", "status": "passed", "time": {"start": 1753764937838, "stop": 1753764946850, "duration": 9012}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "29814f9b6b5bad3852e86d186b662dab"}, {"name": "test_enable_all_ai_magic_box_features", "children": [{"name": "测试enable all ai magic box features返回正确的不支持响应", "uid": "f12e7811277e974e", "parentUid": "9b7a1bd9dfc15c1c490b49730b3b06c6", "status": "passed", "time": {"start": 1753764959912, "stop": 1753764968912, "duration": 9000}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9b7a1bd9dfc15c1c490b49730b3b06c6"}, {"name": "test_enable_auto_pickup", "children": [{"name": "测试enable auto pickup返回正确的不支持响应", "uid": "bca06f3ba9fb151a", "parentUid": "34950724c1fa063bc98eef873eb35771", "status": "passed", "time": {"start": 1753764982371, "stop": 1753764991328, "duration": 8957}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "34950724c1fa063bc98eef873eb35771"}, {"name": "test_enable_brightness_locking", "children": [{"name": "测试enable brightness locking返回正确的不支持响应", "uid": "820e6fda2bc7ebdc", "parentUid": "fb01cd9d24d4da8385ec6db44b9bf596", "status": "passed", "time": {"start": 1753765004516, "stop": 1753765012789, "duration": 8273}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fb01cd9d24d4da8385ec6db44b9bf596"}, {"name": "test_enable_call_on_hold", "children": [{"name": "测试Enable Call on Hold返回正确的不支持响应", "uid": "865d969ffdc78faa", "parentUid": "743e0a22021355320010c1959207f737", "status": "passed", "time": {"start": 1753765026272, "stop": 1753765039213, "duration": 12941}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "743e0a22021355320010c1959207f737"}, {"name": "test_enable_call_rejection", "children": [{"name": "测试Enable Call Rejection返回正确的不支持响应", "uid": "80224d74cee84492", "parentUid": "a7a8abf2aae8cc5f45b540e4f777f037", "status": "passed", "time": {"start": 1753765053607, "stop": 1753765066572, "duration": 12965}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a7a8abf2aae8cc5f45b540e4f777f037"}, {"name": "test_enable_network_enhancement", "children": [{"name": "测试Enable Network Enhancement返回正确的不支持响应", "uid": "9b68c7b52f218f6f", "parentUid": "ecc995aaaa028fb08ab7ae1ab0e7875d", "status": "passed", "time": {"start": 1753765080707, "stop": 1753765088965, "duration": 8258}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ecc995aaaa028fb08ab7ae1ab0e7875d"}, {"name": "test_enable_running_lock", "children": [{"name": "测试enable running lock返回正确的不支持响应", "uid": "ed1dcfed11432025", "parentUid": "d576b618126fc7256f34985c398c1d3b", "status": "passed", "time": {"start": 1753765102958, "stop": 1753765111240, "duration": 8282}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d576b618126fc7256f34985c398c1d3b"}, {"name": "test_enable_touch_optimization", "children": [{"name": "测试enable touch optimization返回正确的不支持响应", "uid": "144fa92bac6d7d3c", "parentUid": "8480a7d7a44bc6838b96d68a4c18aa05", "status": "passed", "time": {"start": 1753765125128, "stop": 1753765133836, "duration": 8708}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8480a7d7a44bc6838b96d68a4c18aa05"}, {"name": "test_enable_unfreeze", "children": [{"name": "测试enable unfreeze返回正确的不支持响应", "uid": "e03fdcb88dd5fa05", "parentUid": "66a93da14003d58b4dfb0006f98aa4c5", "status": "passed", "time": {"start": 1753765147303, "stop": 1753765155709, "duration": 8406}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "66a93da14003d58b4dfb0006f98aa4c5"}, {"name": "test_enable_zonetouch_master", "children": [{"name": "测试enable zonetouch master返回正确的不支持响应", "uid": "4760d5acdc6b4ef1", "parentUid": "8c6899d4ddce4d928d89f60cb07aeea9", "status": "passed", "time": {"start": 1753765170109, "stop": 1753765178909, "duration": 8800}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8c6899d4ddce4d928d89f60cb07aeea9"}, {"name": "test_how_s_the_weather_today", "children": [{"name": "测试how's the weather today?返回正确的不支持响应", "uid": "a3eadc9fba00cf71", "parentUid": "ea255e7426900fa5d6b9f37c2ecbbef5", "status": "failed", "time": {"start": 1753765192515, "stop": 1753765205101, "duration": 12586}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ea255e7426900fa5d6b9f37c2ecbbef5"}, {"name": "test_how_to_set_screenshots", "children": [{"name": "测试how to set screenshots返回正确的不支持响应", "uid": "c8ecf6e0dcefd2e3", "parentUid": "98125eb3c0eb347bf93f6ebbb14e8847", "status": "passed", "time": {"start": 1753765219280, "stop": 1753765227782, "duration": 8502}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "98125eb3c0eb347bf93f6ebbb14e8847"}, {"name": "test_increase_settings_for_special_functions", "children": [{"name": "测试increase settings for special functions返回正确的不支持响应", "uid": "757593af1b0f4165", "parentUid": "849c4d59d6ccd19947474d581af3ccb2", "status": "passed", "time": {"start": 1753765241506, "stop": 1753765253546, "duration": 12040}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "849c4d59d6ccd19947474d581af3ccb2"}, {"name": "test_jump_to_adaptive_brightness_settings", "children": [{"name": "测试jump to adaptive brightness settings返回正确的不支持响应", "uid": "26de3ee7bd066adc", "parentUid": "0355ddc9170b8d072304068f8868f806", "status": "passed", "time": {"start": 1753765267520, "stop": 1753765280068, "duration": 12548}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0355ddc9170b8d072304068f8868f806"}, {"name": "test_jump_to_ai_wallpaper_generator_settings", "children": [{"name": "测试jump to ai wallpaper generator settings返回正确的不支持响应", "uid": "3265a62e2f50eb16", "parentUid": "fb66326ff650d5ed24178fe23a3ec6c7", "status": "passed", "time": {"start": 1753765293770, "stop": 1753765305973, "duration": 12203}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fb66326ff650d5ed24178fe23a3ec6c7"}, {"name": "test_jump_to_auto_rotate_screen_settings", "children": [{"name": "测试jump to auto rotate screen settings返回正确的不支持响应", "uid": "c145d2c6d0bdd37b", "parentUid": "4868992c324cca3aa80f892f5012edcc", "status": "passed", "time": {"start": 1753765319585, "stop": 1753765332134, "duration": 12549}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4868992c324cca3aa80f892f5012edcc"}, {"name": "test_jump_to_battery_and_power_saving", "children": [{"name": "测试jump to battery and power saving返回正确的不支持响应", "uid": "32fa194272e5fe43", "parentUid": "c39df595d254493d9e1cf180846ee18a", "status": "passed", "time": {"start": 1753765345752, "stop": 1753765354649, "duration": 8897}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c39df595d254493d9e1cf180846ee18a"}, {"name": "test_jump_to_battery_usage", "children": [{"name": "测试jump to battery usage返回正确的不支持响应", "uid": "6f7ba473d5caa2ff", "parentUid": "0bd7fb8ef7e8ca2e3a1a1699257e50f9", "status": "passed", "time": {"start": 1753765368389, "stop": 1753765377128, "duration": 8739}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0bd7fb8ef7e8ca2e3a1a1699257e50f9"}, {"name": "test_jump_to_call_notifications", "children": [{"name": "测试jump to call notifications返回正确的不支持响应", "uid": "fd6f74a8afb4623b", "parentUid": "5a05d3bb6cee7f72819e49d4f6d5ee1f", "status": "passed", "time": {"start": 1753765390724, "stop": 1753765403631, "duration": 12907}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5a05d3bb6cee7f72819e49d4f6d5ee1f"}, {"name": "test_jump_to_high_brightness_mode_settings", "children": [{"name": "测试jump to high brightness mode settings返回正确的不支持响应", "uid": "ceced2591885abc6", "parentUid": "4a8b87bfde2b2e8ce7967634be5288ab", "status": "passed", "time": {"start": 1753765417843, "stop": 1753765430253, "duration": 12410}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4a8b87bfde2b2e8ce7967634be5288ab"}, {"name": "test_jump_to_lock_screen_notification_and_display_settings", "children": [{"name": "测试jump to lock screen notification and display settings返回正确的不支持响应", "uid": "8808f7aabaffe30e", "parentUid": "cfbc1116cbdd7d5906a09ffc432b57db", "status": "passed", "time": {"start": 1753765443894, "stop": 1753765456708, "duration": 12814}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cfbc1116cbdd7d5906a09ffc432b57db"}, {"name": "test_jump_to_notifications_and_status_bar_settings", "children": [{"name": "测试jump to notifications and status bar settings返回正确的不支持响应", "uid": "c297d430a8b59844", "parentUid": "f6098b8dc4a6271dec4f5e5f57bd4752", "status": "passed", "time": {"start": 1753765470565, "stop": 1753765483040, "duration": 12475}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f6098b8dc4a6271dec4f5e5f57bd4752"}, {"name": "test_more_settings", "children": [{"name": "测试more settings返回正确的不支持响应", "uid": "b6291b5bdf4cb29d", "parentUid": "ca0b29ba7554057ab3ba8216b6b1f0ba", "status": "passed", "time": {"start": 1753765496809, "stop": 1753765508975, "duration": 12166}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ca0b29ba7554057ab3ba8216b6b1f0ba"}, {"name": "test_open_font_family_settings", "children": [{"name": "测试open font family settings返回正确的不支持响应", "uid": "f7620d96e89fe416", "parentUid": "5d1c4a01c8558acb00bb5c6e528035a3", "status": "passed", "time": {"start": 1753765522695, "stop": 1753765534823, "duration": 12128}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5d1c4a01c8558acb00bb5c6e528035a3"}, {"name": "test_open_notification_ringtone_settings", "children": [{"name": "测试open notification ringtone settings返回正确的不支持响应", "uid": "895239a3cabc886", "parentUid": "309e531c38122a270a22145c2b2f4a8a", "status": "passed", "time": {"start": 1753765548511, "stop": 1753765560850, "duration": 12339}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "309e531c38122a270a22145c2b2f4a8a"}, {"name": "test_order_a_burger", "children": [{"name": "测试order a burger返回正确的不支持响应", "uid": "8600fc1e69aa8275", "parentUid": "12a853573a3ed32b45369299151ae46b", "status": "passed", "time": {"start": 1753765574537, "stop": 1753765583369, "duration": 8832}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "12a853573a3ed32b45369299151ae46b"}, {"name": "test_order_a_takeaway", "children": [{"name": "测试order a takeaway返回正确的不支持响应", "uid": "9f6d55c7b99aa986", "parentUid": "6116229fe40039026544aa05527a4376", "status": "passed", "time": {"start": 1753765597096, "stop": 1753765605451, "duration": 8355}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6116229fe40039026544aa05527a4376"}, {"name": "test_reset_phone", "children": [{"name": "测试reset phone返回正确的不支持响应", "uid": "4566d78d4a8b1e24", "parentUid": "97792796a4c38ae78b9db124406767b2", "status": "passed", "time": {"start": 1753765618958, "stop": 1753765632284, "duration": 13326}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "97792796a4c38ae78b9db124406767b2"}, {"name": "test_set_app_auto_rotate", "children": [{"name": "测试set app auto rotate返回正确的不支持响应", "uid": "55306a20cf18accf", "parentUid": "42a0d4edd729031d01ed4ce54f32a86f", "status": "passed", "time": {"start": 1753765645878, "stop": 1753765655018, "duration": 9140}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "42a0d4edd729031d01ed4ce54f32a86f"}, {"name": "test_set_app_notifications", "children": [{"name": "测试set app notifications返回正确的不支持响应", "uid": "a934d12461417644", "parentUid": "f17166693c717b681b5c4ace219a5254", "status": "passed", "time": {"start": 1753765668208, "stop": 1753765677125, "duration": 8917}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f17166693c717b681b5c4ace219a5254"}, {"name": "test_set_battery_saver_settings", "children": [{"name": "测试set battery saver settings返回正确的不支持响应", "uid": "6d09de9067475c54", "parentUid": "90688cbd93c016ee7d407e660ac88b04", "status": "passed", "time": {"start": 1753765690785, "stop": 1753765703376, "duration": 12591}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "90688cbd93c016ee7d407e660ac88b04"}, {"name": "test_set_call_back_with_last_used_sim", "children": [{"name": "测试set call back with last used sim返回正确的不支持响应", "uid": "ff43e38cf6639396", "parentUid": "27c7eefc61a4f26939cc8e62add52a75", "status": "passed", "time": {"start": 1753765717082, "stop": 1753765730096, "duration": 13014}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "27c7eefc61a4f26939cc8e62add52a75"}, {"name": "test_set_color_style", "children": [{"name": "测试set color style返回正确的不支持响应", "uid": "afaa736411383736", "parentUid": "4a9eb6eed41e8d978e3f2271d25aeeb9", "status": "passed", "time": {"start": 1753765744290, "stop": 1753765752878, "duration": 8588}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4a9eb6eed41e8d978e3f2271d25aeeb9"}, {"name": "test_set_compatibility_mode", "children": [{"name": "测试set compatibility mode返回正确的不支持响应", "uid": "1aef1ccd7dac8693", "parentUid": "71f9d2696419dde9e2fbc887cd35352f", "status": "passed", "time": {"start": 1753765767158, "stop": 1753765775913, "duration": 8755}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "71f9d2696419dde9e2fbc887cd35352f"}, {"name": "test_set_cover_screen_apps", "children": [{"name": "测试set cover screen apps返回正确的不支持响应", "uid": "4f630659388f04a8", "parentUid": "3e469638b07e0f748f137f566308ee39", "status": "passed", "time": {"start": 1753765789622, "stop": 1753765797895, "duration": 8273}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3e469638b07e0f748f137f566308ee39"}, {"name": "test_set_customized_cover_screen", "children": [{"name": "测试set customized cover screen返回正确的不支持响应", "uid": "9bc73e51a27bea6f", "parentUid": "681063f39b4b3d4670516dc945bc7e8f", "status": "passed", "time": {"start": 1753765811458, "stop": 1753765819846, "duration": 8388}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "681063f39b4b3d4670516dc945bc7e8f"}, {"name": "test_set_date_time", "children": [{"name": "测试set date & time返回正确的不支持响应", "uid": "f098baef018951df", "parentUid": "55afb5474f6367c035d3c3b4c5aa4a0e", "status": "passed", "time": {"start": 1753765833804, "stop": 1753765842611, "duration": 8807}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "55afb5474f6367c035d3c3b4c5aa4a0e"}, {"name": "test_set_edge_mistouch_prevention", "children": [{"name": "测试set edge mistouch prevention返回正确的不支持响应", "uid": "b790b402ce19d974", "parentUid": "d063298b5e7b7ba1dd78f28933196633", "status": "passed", "time": {"start": 1753765856158, "stop": 1753765864716, "duration": 8558}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d063298b5e7b7ba1dd78f28933196633"}, {"name": "test_set_flex_still_mode", "children": [{"name": "测试set flex-still mode返回正确的不支持响应", "uid": "2f290e7ec2fa59be", "parentUid": "fb37e27acd491ebde3cb6675b3ff4b33", "status": "passed", "time": {"start": 1753765878872, "stop": 1753765887490, "duration": 8618}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fb37e27acd491ebde3cb6675b3ff4b33"}, {"name": "test_set_flip_case_feature", "children": [{"name": "测试set flip case feature返回正确的不支持响应", "uid": "98bb8c35dbf6e67f", "parentUid": "d093123b5193b9aead35dd1996c7cd1a", "status": "passed", "time": {"start": 1753765901173, "stop": 1753765909427, "duration": 8254}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d093123b5193b9aead35dd1996c7cd1a"}, {"name": "test_set_floating_windows", "children": [{"name": "测试set floating windows返回正确的不支持响应", "uid": "d1f215605f21a61a", "parentUid": "260060301e90f5edbed593882a5fec0d", "status": "passed", "time": {"start": 1753765923195, "stop": 1753765931855, "duration": 8660}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "260060301e90f5edbed593882a5fec0d"}, {"name": "test_set_folding_screen_zone", "children": [{"name": "测试set folding screen zone返回正确的不支持响应", "uid": "881ccc6117d75d5b", "parentUid": "d7d6eb8f64580ac8b8fb024a954198fa", "status": "passed", "time": {"start": 1753765945477, "stop": 1753765953957, "duration": 8480}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d7d6eb8f64580ac8b8fb024a954198fa"}, {"name": "test_set_font_size", "children": [{"name": "测试set font size返回正确的不支持响应", "uid": "2d67f42529f4ba11", "parentUid": "740d4f9856207354991fc052a0633f21", "status": "passed", "time": {"start": 1753765967757, "stop": 1753765976842, "duration": 9085}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "740d4f9856207354991fc052a0633f21"}, {"name": "test_set_gesture_navigation", "children": [{"name": "测试set gesture navigation返回正确的不支持响应", "uid": "ca8b062a81e447d0", "parentUid": "47320f9ee2ac872ac9be9e52d1af28d8", "status": "passed", "time": {"start": 1753765990851, "stop": 1753766003692, "duration": 12841}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "47320f9ee2ac872ac9be9e52d1af28d8"}, {"name": "test_set_languages", "children": [{"name": "测试set languages返回正确的不支持响应", "uid": "523aa5be0a30d5f7", "parentUid": "46ab090271dad35a2f22e1aa891d167d", "status": "passed", "time": {"start": 1753766017621, "stop": 1753766026454, "duration": 8833}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "46ab090271dad35a2f22e1aa891d167d"}, {"name": "test_set_lockscreen_passwords", "children": [{"name": "测试set lockscreen passwords返回正确的不支持响应", "uid": "7373dd251d127d09", "parentUid": "1ebb1fb5844374f960b3ecd76cddec92", "status": "passed", "time": {"start": 1753766040123, "stop": 1753766048747, "duration": 8624}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1ebb1fb5844374f960b3ecd76cddec92"}, {"name": "test_set_my_fonts", "children": [{"name": "测试set my fonts返回正确的不支持响应", "uid": "6dbcf1976884cdf8", "parentUid": "c63b678cbbe7de452848a070298e3fab", "status": "passed", "time": {"start": 1753766062593, "stop": 1753766071390, "duration": 8797}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c63b678cbbe7de452848a070298e3fab"}, {"name": "test_set_my_themes", "children": [{"name": "测试set my themes返回正确的不支持响应", "uid": "75008c9c41959d3b", "parentUid": "452ed970a52e7ee9049bbdb424f9b57d", "status": "passed", "time": {"start": 1753766085120, "stop": 1753766093577, "duration": 8457}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "452ed970a52e7ee9049bbdb424f9b57d"}, {"name": "test_set_parallel_windows", "children": [{"name": "测试set parallel windows返回正确的不支持响应", "uid": "feaafdb7aa1aa06", "parentUid": "b7b2179becb10c93603c2fcce15e0006", "status": "passed", "time": {"start": 1753766107022, "stop": 1753766115543, "duration": 8521}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b7b2179becb10c93603c2fcce15e0006"}, {"name": "test_set_personal_hotspot", "children": [{"name": "测试set personal hotspot返回正确的不支持响应", "uid": "35097e6128df904c", "parentUid": "4fed48034322a5d5119c937240c4aa77", "status": "passed", "time": {"start": 1753766129315, "stop": 1753766138465, "duration": 9150}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4fed48034322a5d5119c937240c4aa77"}, {"name": "test_set_phantom_v_pen", "children": [{"name": "测试set phantom v pen返回正确的不支持响应", "uid": "7498ca1bd63270a0", "parentUid": "7b8efe12e2cc4a1e4fec01a67e1ead0d", "status": "passed", "time": {"start": 1753766152299, "stop": 1753766161117, "duration": 8818}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7b8efe12e2cc4a1e4fec01a67e1ead0d"}, {"name": "test_set_phone_number", "children": [{"name": "测试set phone number返回正确的不支持响应", "uid": "f72ab1da7fdcbd71", "parentUid": "f0d8d3a4b043370c607b0bb64b8df1bd", "status": "passed", "time": {"start": 1753766175014, "stop": 1753766188180, "duration": 13166}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f0d8d3a4b043370c607b0bb64b8df1bd"}, {"name": "test_set_scheduled_power_on_off_and_restart", "children": [{"name": "测试set scheduled power on/off and restart返回正确的不支持响应", "uid": "c7a4a9bb9cd3d2d1", "parentUid": "2095cb76657298bca8aa08a5ddc4452c", "status": "passed", "time": {"start": 1753766202038, "stop": 1753766211140, "duration": 9102}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2095cb76657298bca8aa08a5ddc4452c"}, {"name": "test_set_screen_refresh_rate", "children": [{"name": "测试set screen refresh rate返回正确的不支持响应", "uid": "1b42bcf3479561c8", "parentUid": "d9cca6f6b44bb5f9a4608c3db7a6bb1b", "status": "passed", "time": {"start": 1753766224733, "stop": 1753766233136, "duration": 8403}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d9cca6f6b44bb5f9a4608c3db7a6bb1b"}, {"name": "test_set_screen_relay", "children": [{"name": "测试set screen relay返回正确的不支持响应", "uid": "a9ae4477c0b948a6", "parentUid": "66dc5bdcc3e27c2ae07a1536c41488cd", "status": "passed", "time": {"start": 1753766247545, "stop": 1753766256691, "duration": 9146}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "66dc5bdcc3e27c2ae07a1536c41488cd"}, {"name": "test_set_screen_timeout", "children": [{"name": "测试set screen timeout返回正确的不支持响应", "uid": "33e1b9f38d32945b", "parentUid": "efb5e97f214d22c0692101b1024ecde2", "status": "passed", "time": {"start": 1753766270460, "stop": 1753766279132, "duration": 8672}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "efb5e97f214d22c0692101b1024ecde2"}, {"name": "test_set_screen_to_minimum_brightness", "children": [{"name": "测试set screen to minimum brightness返回正确的不支持响应", "uid": "1d57845f41e6a5f8", "parentUid": "e1c10086104522b2fbc3cd552709a125", "status": "passed", "time": {"start": 1753766292819, "stop": 1753766301429, "duration": 8610}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e1c10086104522b2fbc3cd552709a125"}, {"name": "test_set_sim_ringtone", "children": [{"name": "测试set sim1 ringtone返回正确的不支持响应", "uid": "dc061218f29f8e09", "parentUid": "02b906bfcb368697dfb809905922e9cb", "status": "passed", "time": {"start": 1753766315363, "stop": 1753766324154, "duration": 8791}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "02b906bfcb368697dfb809905922e9cb"}, {"name": "test_set_smart_hub", "children": [{"name": "测试set smart hub返回正确的不支持响应", "uid": "2d689da05d191cd8", "parentUid": "1b885e8f75c6cb8827815cfc72755c55", "status": "passed", "time": {"start": 1753766337954, "stop": 1753766346724, "duration": 8770}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1b885e8f75c6cb8827815cfc72755c55"}, {"name": "test_set_smart_panel", "children": [{"name": "测试set smart panel返回正确的不支持响应", "uid": "c289979a8e3cbf4", "parentUid": "116ddf2803a2d3fa8977f8f2b43e59a3", "status": "passed", "time": {"start": 1753766360497, "stop": 1753766369063, "duration": 8566}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "116ddf2803a2d3fa8977f8f2b43e59a3"}, {"name": "test_set_special_function", "children": [{"name": "测试set special function返回正确的不支持响应", "uid": "fc74c441f7f7fac8", "parentUid": "1cdef3d89962b3a41ead4e6a44623984", "status": "passed", "time": {"start": 1753766382944, "stop": 1753766391498, "duration": 8554}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1cdef3d89962b3a41ead4e6a44623984"}, {"name": "test_set_split_screen_apps", "children": [{"name": "测试set split-screen apps返回正确的不支持响应", "uid": "94d5be3d7b73138a", "parentUid": "df5405aa0bd507fb6dc22f263be7dfef", "status": "passed", "time": {"start": 1753766405436, "stop": 1753766415096, "duration": 9660}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "df5405aa0bd507fb6dc22f263be7dfef"}, {"name": "test_set_timezone", "children": [{"name": "测试set timezone返回正确的不支持响应", "uid": "e787218d61e83f91", "parentUid": "9e7c120412bd7e0f730e61377785fa8a", "status": "passed", "time": {"start": 1753766428807, "stop": 1753766437224, "duration": 8417}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9e7c120412bd7e0f730e61377785fa8a"}, {"name": "test_set_ultra_power_saving", "children": [{"name": "测试set ultra power saving返回正确的不支持响应", "uid": "c3dca0ed7665226e", "parentUid": "5cae1c2c93d377af57591ba886a396b7", "status": "passed", "time": {"start": 1753766451006, "stop": 1753766460089, "duration": 9083}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5cae1c2c93d377af57591ba886a396b7"}, {"name": "test_switch_to_equilibrium_mode", "children": [{"name": "测试switch to equilibrium mode返回正确的不支持响应", "uid": "ce8424deaa12d0d3", "parentUid": "eaf66c431b1bc4cdb440f63e804d5b7f", "status": "passed", "time": {"start": 1753766474322, "stop": 1753766483379, "duration": 9057}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "eaf66c431b1bc4cdb440f63e804d5b7f"}, {"name": "test_switch_to_performance_mode", "children": [{"name": "测试switch to performance mode返回正确的不支持响应", "uid": "b9ad5f0890d7ff95", "parentUid": "9c64338b2e6a23f5945b6c45a6e8988f", "status": "passed", "time": {"start": 1753766497236, "stop": 1753766505493, "duration": 8257}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9c64338b2e6a23f5945b6c45a6e8988f"}, {"name": "test_switch_to_power_saving_mode", "children": [{"name": "测试switch to power saving mode返回正确的不支持响应", "uid": "509ae9b5f4ed11e1", "parentUid": "0a89b8197df33175fe755b5815c5229b", "status": "passed", "time": {"start": 1753766519465, "stop": 1753766528222, "duration": 8757}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0a89b8197df33175fe755b5815c5229b"}, {"name": "test_switching_charging_speed", "children": [{"name": "测试switching charging speed返回正确的不支持响应", "uid": "a651e04c6a3ccba0", "parentUid": "556ad59feb219cf0e64a09e0c312ef81", "status": "passed", "time": {"start": 1753766542302, "stop": 1753766550938, "duration": 8636}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "556ad59feb219cf0e64a09e0c312ef81"}, {"name": "test_the_second", "children": [{"name": "测试the second返回正确的不支持响应", "uid": "487f42ee1097884f", "parentUid": "33f765d60f4eb02aa8bf10da2e90a0c5", "status": "passed", "time": {"start": 1753766564387, "stop": 1753766572770, "duration": 8383}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "33f765d60f4eb02aa8bf10da2e90a0c5"}, {"name": "test_turn_off_driving_mode", "children": [{"name": "测试turn off driving mode返回正确的不支持响应", "uid": "5e296627e553497f", "parentUid": "07d91c6eb72b9bf62af23569018b6ed9", "status": "passed", "time": {"start": 1753766586941, "stop": 1753766595841, "duration": 8900}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "07d91c6eb72b9bf62af23569018b6ed9"}, {"name": "test_turn_off_show_battery_percentage", "children": [{"name": "测试turn off show battery percentage返回正确的不支持响应", "uid": "17d22617af07582d", "parentUid": "1da9f8360bfb0009b7bbba3b558b5ad6", "status": "passed", "time": {"start": 1753766609664, "stop": 1753766618172, "duration": 8508}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1da9f8360bfb0009b7bbba3b558b5ad6"}, {"name": "test_turn_on_driving_mode", "children": [{"name": "测试turn on driving mode返回正确的不支持响应", "uid": "6854011c63695983", "parentUid": "fd2d2a21ef2aa878399c88b3d0e934ba", "status": "passed", "time": {"start": 1753766631818, "stop": 1753766640135, "duration": 8317}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fd2d2a21ef2aa878399c88b3d0e934ba"}, {"name": "test_turn_on_high_brightness_mode", "children": [{"name": "测试turn on high brightness mode返回正确的不支持响应", "uid": "246bdbd532071352", "parentUid": "49b44a51671f9fc053854051c80f7c70", "status": "passed", "time": {"start": 1753766654111, "stop": 1753766662935, "duration": 8824}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "49b44a51671f9fc053854051c80f7c70"}, {"name": "test_turn_on_show_battery_percentage", "children": [{"name": "测试turn on show battery percentage返回正确的不支持响应", "uid": "6666371aa4b73f76", "parentUid": "838b34c8fa65f970357a94fb9eaf0dcc", "status": "passed", "time": {"start": 1753766676863, "stop": 1753766685957, "duration": 9094}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "838b34c8fa65f970357a94fb9eaf0dcc"}, {"name": "test_voice_setting_page", "children": [{"name": "测试Voice setting page返回正确的不支持响应", "uid": "1b1585fb19777980", "parentUid": "c2e243e17e6068d0d67070f2c20e8486", "status": "passed", "time": {"start": 1753766700113, "stop": 1753766712922, "duration": 12809}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c2e243e17e6068d0d67070f2c20e8486"}, {"name": "test_yandex_eats", "children": [{"name": "测试yandex eats返回正确的不支持响应", "uid": "1991f1f1174d50bb", "parentUid": "ffa74429a7c2fa0151c7e7ef27b25ecb", "status": "passed", "time": {"start": 1753766727520, "stop": 1753766736122, "duration": 8602}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ffa74429a7c2fa0151c7e7ef27b25ecb"}], "uid": "testcases.test_ella.unsupported_commands"}]}