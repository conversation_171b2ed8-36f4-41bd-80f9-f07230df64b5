#!/usr/bin/env python3
"""
页面文本抓取功能演示脚本
展示如何使用BaseEllaTest中新添加的页面文本抓取方法
"""
import sys
import os
import time
import json

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from testcases.test_ella.base_ella_test import BaseEllaTest
from pages.apps.ella.dialogue_page import EllaDialoguePage
from core.logger import log


class PageTextExtractionDemo(BaseEllaTest):
    """页面文本抓取演示类"""
    
    def demo_extract_current_page_text(self):
        """演示抓取当前页面文本"""
        try:
            log.info("🎯 演示：抓取当前页面文本")
            log.info("=" * 50)
            
            # 抓取当前页面文本
            page_text_data = self._get_page_text(None)  # ella_app参数在这个演示中不使用
            
            if page_text_data['success']:
                log.info("✅ 页面文本抓取成功！")
                
                # 显示基本信息
                app_info = page_text_data['current_app']
                log.info(f"当前应用: {app_info['package']}")
                log.info(f"Activity: {app_info['activity']}")
                log.info(f"文本元素数量: {page_text_data['text_count']}")
                
                # 显示页面结构信息
                structure = page_text_data['page_structure']
                log.info(f"屏幕尺寸: {structure['screen_size']}")
                log.info(f"UI元素统计: {structure['element_counts']}")
                
                # 显示前10个文本内容
                log.info("\n📝 页面文本内容（前10个）:")
                for i, text_item in enumerate(page_text_data['all_texts'][:10]):
                    text = text_item['text']
                    if len(text) > 60:
                        text = text[:57] + "..."
                    log.info(f"  {i+1}. [{text_item['type']}] {text}")
                
                # 生成摘要
                summary = self.get_page_text_summary(page_text_data)
                log.info(f"\n📊 页面摘要:\n{summary}")
                
                # 保存到文件
                file_path = self.save_page_text_to_file(page_text_data)
                if file_path:
                    log.info(f"💾 文本数据已保存到: {file_path}")
                
                return page_text_data
                
            else:
                log.error("❌ 页面文本抓取失败")
                error = page_text_data.get('error', 'Unknown error')
                log.error(f"错误信息: {error}")
                return None
                
        except Exception as e:
            log.error(f"❌ 演示过程中发生错误: {e}")
            return None
    
    def demo_extract_multiple_apps(self):
        """演示抓取多个应用的页面文本"""
        try:
            log.info("🎯 演示：抓取多个应用的页面文本")
            log.info("=" * 50)
            
            # 要测试的应用包名列表
            test_apps = [
                "com.android.settings",      # 设置
                "com.android.contacts",      # 联系人
                "com.android.calculator2",   # 计算器
                "com.android.gallery3d",     # 图库
            ]
            
            results = {}
            
            for app_package in test_apps:
                try:
                    log.info(f"\n🚀 启动应用: {app_package}")
                    
                    # 启动应用
                    from core.base_driver import driver_manager
                    driver_manager.start_app(app_package)
                    time.sleep(3)  # 等待应用启动
                    
                    # 抓取页面文本
                    page_text_data = self._get_page_text(None)
                    
                    if page_text_data['success']:
                        log.info(f"✅ {app_package} 文本抓取成功，共 {page_text_data['text_count']} 个文本元素")
                        results[app_package] = page_text_data
                        
                        # 保存单独的文件
                        filename = f"page_text_{app_package.split('.')[-1]}_{int(time.time())}.json"
                        self.save_page_text_to_file(page_text_data, filename)
                        
                    else:
                        log.warning(f"⚠️ {app_package} 文本抓取失败")
                        results[app_package] = None
                    
                    # 停止应用
                    driver_manager.stop_app(app_package)
                    time.sleep(1)
                    
                except Exception as e:
                    log.error(f"❌ 处理应用 {app_package} 时出错: {e}")
                    results[app_package] = None
            
            # 生成总结报告
            log.info("\n📊 多应用文本抓取总结:")
            successful_count = 0
            total_texts = 0
            
            for app_package, data in results.items():
                if data and data['success']:
                    successful_count += 1
                    total_texts += data['text_count']
                    log.info(f"  ✅ {app_package}: {data['text_count']} 个文本元素")
                else:
                    log.info(f"  ❌ {app_package}: 抓取失败")
            
            log.info(f"\n成功率: {successful_count}/{len(test_apps)} ({successful_count/len(test_apps)*100:.1f}%)")
            log.info(f"总文本元素数: {total_texts}")
            
            return results
            
        except Exception as e:
            log.error(f"❌ 多应用演示过程中发生错误: {e}")
            return {}
    
    def demo_text_analysis(self):
        """演示文本分析功能"""
        try:
            log.info("🎯 演示：页面文本分析")
            log.info("=" * 50)
            
            # 抓取当前页面文本
            page_text_data = self._get_page_text(None)
            
            if not page_text_data['success']:
                log.error("❌ 无法获取页面文本，跳过分析")
                return
            
            texts = page_text_data['all_texts']
            
            # 分析1: 文本长度分布
            log.info("📊 文本长度分布:")
            length_ranges = {
                '1-5字符': 0,
                '6-15字符': 0, 
                '16-30字符': 0,
                '31-50字符': 0,
                '50+字符': 0
            }
            
            for text_item in texts:
                length = len(text_item['text'])
                if length <= 5:
                    length_ranges['1-5字符'] += 1
                elif length <= 15:
                    length_ranges['6-15字符'] += 1
                elif length <= 30:
                    length_ranges['16-30字符'] += 1
                elif length <= 50:
                    length_ranges['31-50字符'] += 1
                else:
                    length_ranges['50+字符'] += 1
            
            for range_name, count in length_ranges.items():
                percentage = count / len(texts) * 100 if texts else 0
                log.info(f"  {range_name}: {count} 个 ({percentage:.1f}%)")
            
            # 分析2: 元素类型分布
            log.info("\n📊 UI元素类型分布:")
            type_counts = {}
            for text_item in texts:
                element_type = text_item['type']
                type_counts[element_type] = type_counts.get(element_type, 0) + 1
            
            for element_type, count in sorted(type_counts.items(), key=lambda x: x[1], reverse=True):
                percentage = count / len(texts) * 100 if texts else 0
                log.info(f"  {element_type}: {count} 个 ({percentage:.1f}%)")
            
            # 分析3: 常见关键词
            log.info("\n📊 常见关键词分析:")
            all_text = " ".join([item['text'] for item in texts]).lower()
            
            # 简单的关键词统计
            common_keywords = ['设置', '确定', '取消', '返回', '搜索', '菜单', '更多', '完成', '保存']
            keyword_counts = {}
            
            for keyword in common_keywords:
                count = all_text.count(keyword.lower())
                if count > 0:
                    keyword_counts[keyword] = count
            
            if keyword_counts:
                for keyword, count in sorted(keyword_counts.items(), key=lambda x: x[1], reverse=True):
                    log.info(f"  '{keyword}': 出现 {count} 次")
            else:
                log.info("  未找到常见关键词")
            
            # 分析4: 最长和最短文本
            if texts:
                longest_text = max(texts, key=lambda x: len(x['text']))
                shortest_text = min(texts, key=lambda x: len(x['text']))
                
                log.info(f"\n📊 文本长度极值:")
                log.info(f"  最长文本 ({len(longest_text['text'])} 字符): {longest_text['text'][:100]}...")
                log.info(f"  最短文本 ({len(shortest_text['text'])} 字符): {shortest_text['text']}")
            
        except Exception as e:
            log.error(f"❌ 文本分析过程中发生错误: {e}")


def main():
    """主函数"""
    log.info("🎯 页面文本抓取功能演示")
    log.info("=" * 80)
    
    demo = PageTextExtractionDemo()
    
    try:
        # 演示1: 抓取当前页面文本
        log.info("\n" + "="*60)
        demo.demo_extract_current_page_text()
        
        # 演示2: 文本分析
        log.info("\n" + "="*60)
        demo.demo_text_analysis()
        
        # 询问是否进行多应用演示
        user_input = input("\n是否进行多应用文本抓取演示？(y/n): ")
        if user_input.lower() in ['y', 'yes']:
            log.info("\n" + "="*60)
            demo.demo_extract_multiple_apps()
        
        log.info("\n🎉 演示完成！")
        return 0
        
    except Exception as e:
        log.error(f"❌ 演示过程中发生错误: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
