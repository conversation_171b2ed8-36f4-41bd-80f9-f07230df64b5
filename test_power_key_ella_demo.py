#!/usr/bin/env python3
"""
演示脚本：测试在AI图库打开图片状态下长按电源键调出Ella对话页面
用于验证测试用例的基本功能
"""
import sys
import os
import time

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from pages.base.detectors.ai_gallery_detector import AiGalleryDetector
from pages.apps.ella.dialogue_page import EllaDialoguePage
from core.logger import log
from core.base_driver import driver_manager


def demo_power_key_invoke_ella():
    """演示长按电源键调出Ella的功能"""
    log.info("=" * 60)
    log.info("🚀 开始演示：在AI图库中长按电源键调出Ella")
    log.info("=" * 60)
    
    ai_gallery_detector = AiGalleryDetector()
    ella_page = EllaDialoguePage()
    
    try:
        # 步骤1: 检查AI图库应用
        log.info("📱 步骤1: 检查AI图库应用")
        ai_gallery_packages = ai_gallery_detector.get_package_names()
        log.info(f"AI图库包名列表: {ai_gallery_packages}")
        
        # 步骤2: 尝试启动AI图库
        log.info("🚀 步骤2: 尝试启动AI图库")
        ai_gallery_started = False
        current_package = None
        
        for package in ai_gallery_packages:
            try:
                log.info(f"尝试启动: {package}")
                driver_manager.start_app(package)
                time.sleep(3)
                
                if ai_gallery_detector.is_app_running():
                    log.info(f"✅ AI图库启动成功: {package}")
                    ai_gallery_started = True
                    current_package = package
                    break
                else:
                    log.warning(f"❌ {package} 启动失败或未运行")
            except Exception as e:
                log.warning(f"启动{package}异常: {e}")
                continue
        
        if not ai_gallery_started:
            log.error("❌ 所有AI图库包都启动失败")
            return False
        
        # 步骤3: 截图记录当前状态
        log.info("📸 步骤3: 截图记录AI图库状态")
        screenshot_path = driver_manager.screenshot("ai_gallery_demo.png")
        log.info(f"截图保存: {screenshot_path}")
        
        # 步骤4: 检查Ella应用状态
        log.info("🤖 步骤4: 检查Ella应用状态")
        ella_running_before = ella_page._check_app_started("com.transsion.aivoiceassistant")
        log.info(f"长按前Ella运行状态: {ella_running_before}")
        
        # 步骤5: 模拟长按电源键（这里只是演示，不实际执行）
        log.info("🔋 步骤5: 模拟长按电源键（演示模式）")
        log.info("注意：在实际测试中，这里会执行长按电源键操作")
        log.info("由于安全考虑，演示模式不会实际按电源键")

        # 步骤6: 演示悬浮窗检测
        log.info("🔍 步骤6: 演示悬浮窗检测方法")
        floating_detected = demo_floating_window_detection()
        log.info(f"悬浮窗检测结果: {floating_detected}")
        
        # 步骤7: 检查设备信息
        log.info("📱 步骤7: 检查设备信息")
        try:
            device_info = driver_manager.get_device_info()
            log.info(f"设备信息: {device_info}")
        except Exception as e:
            log.warning(f"获取设备信息失败: {e}")
        
        # 步骤8: 清理
        log.info("🧹 步骤8: 清理环境")
        try:
            driver_manager.stop_app(current_package)
            log.info(f"✅ 已停止AI图库应用: {current_package}")
        except Exception as e:
            log.warning(f"停止应用失败: {e}")
        
        log.info("✅ 演示完成")
        return True
        
    except Exception as e:
        log.error(f"❌ 演示过程中发生错误: {e}")
        return False


def demo_floating_window_detection():
    """演示悬浮窗检测方法"""
    try:
        log.info("🔍 演示悬浮窗检测方法")

        # 方法1: 检查当前焦点窗口
        log.info("方法1: 检查当前焦点窗口")
        try:
            import subprocess

            # 检查当前焦点窗口
            focus_result = subprocess.run(
                ["adb", "shell", "dumpsys", "window", "|", "findstr", "mCurrentFocus"],
                capture_output=True, text=True, timeout=5, shell=True
            )

            if focus_result.returncode == 0:
                focus_output = focus_result.stdout.strip()
                log.info(f"当前焦点窗口: {focus_output}")

                if "aivoiceassistant_Window" in focus_output:
                    log.info("✅ 检测到Ella悬浮窗（通过焦点窗口）")
                else:
                    log.info("❌ 当前焦点不是Ella悬浮窗")
            else:
                log.warning("获取焦点窗口信息失败")

            # 检查所有窗口信息
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "window", "windows"],
                capture_output=True, text=True, timeout=10
            )

            if result.returncode == 0:
                window_output = result.stdout
                ella_windows = []

                lines = window_output.split('\n')
                for line in lines:
                    if "aivoiceassistant" in line.lower():
                        ella_windows.append(line.strip())

                if ella_windows:
                    log.info(f"✅ 找到{len(ella_windows)}个Ella相关窗口:")
                    for window in ella_windows[:3]:  # 只显示前3个
                        log.info(f"  - {window}")
                else:
                    log.info("❌ 未找到Ella相关窗口")

        except Exception as e:
            log.warning(f"窗口信息检查失败: {e}")

        # 方法2: 检查UI元素
        log.info("方法2: 检查UI元素")
        try:
            # 查找Ella应用的元素
            ella_elements = driver_manager.driver(packageName="com.transsion.aivoiceassistant")
            if ella_elements.exists(timeout=2):
                count = ella_elements.count
                log.info(f"✅ 找到{count}个Ella应用元素")
            else:
                log.info("❌ 未找到Ella应用元素")

        except Exception as e:
            log.warning(f"UI元素检查失败: {e}")

        # 方法3: 检查特定文本
        log.info("方法3: 检查特征文本")
        try:
            ella_texts = ["Ella", "语音助手", "AI助手"]
            found_texts = []

            for text in ella_texts:
                try:
                    element = driver_manager.driver(text=text)
                    if element.exists(timeout=1):
                        found_texts.append(text)
                except:
                    continue

            if found_texts:
                log.info(f"✅ 找到特征文本: {found_texts}")
            else:
                log.info("❌ 未找到特征文本")

        except Exception as e:
            log.warning(f"文本检查失败: {e}")

        log.info("悬浮窗检测演示完成")
        return True

    except Exception as e:
        log.error(f"悬浮窗检测演示失败: {e}")
        return False


def check_dependencies():
    """检查依赖项"""
    log.info("🔍 检查依赖项...")
    
    try:
        # 检查driver_manager
        log.info("检查driver_manager...")
        device_info = driver_manager.get_device_info()
        log.info(f"✅ driver_manager正常，设备: {device_info.get('model', 'Unknown')}")
        
        # 检查AI图库检测器
        log.info("检查AI图库检测器...")
        ai_detector = AiGalleryDetector()
        packages = ai_detector.get_package_names()
        log.info(f"✅ AI图库检测器正常，包名: {packages}")
        
        # 检查Ella页面
        log.info("检查Ella页面...")
        ella_page = EllaDialoguePage()
        log.info("✅ Ella页面类正常")
        
        return True
        
    except Exception as e:
        log.error(f"❌ 依赖项检查失败: {e}")
        return False


def main():
    """主函数"""
    log.info("🎯 AI图库长按电源键调出Ella - 演示脚本")
    log.info("=" * 80)
    
    # 检查依赖项
    if not check_dependencies():
        log.error("❌ 依赖项检查失败，退出演示")
        return 1
    
    # 运行演示
    if demo_power_key_invoke_ella():
        log.info("🎉 演示成功完成")
        return 0
    else:
        log.error("❌ 演示失败")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
