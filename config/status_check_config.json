{"status_check_config": {"bluetooth": {"keywords": ["bluetooth", "open bt", "蓝牙"], "initial_method": "check_bluetooth_status", "final_method": "check_bluetooth_status_smart", "description": "蓝牙状态"}, "contacts": {"keywords": ["contact", "contacts", "联系人", "通讯录", "dialer", "call", "redial", "open phone", "make a phone call"], "initial_method": "check_contacts_app_opened", "final_method": "check_contacts_app_opened_smart", "description": "联系人应用状态"}, "weather": {"keywords": ["weather", "天气"], "initial_method": "check_weather_app_opened", "final_method": "check_weather_app_opened", "description": "天气应用状态"}, "camera": {"keywords": ["camera", "take a selfie", "photo", "相机", "拍照"], "initial_method": "check_camera_app_opened", "final_method": "check_camera_app_opened", "description": "相机应用状态"}, "visha": {"keywords": ["play music", "play afro strut", "play sun be song of jide chord", "play rock music", "play jay chou's music"], "initial_method": "check_visha_app_opened", "final_method": "check_visha_app_opened", "description": "相机应用状态"}, "wifi": {"keywords": ["wifi", "wi-fi", "无线网络"], "initial_method": "check_wifi_status", "final_method": "check_wifi_status", "description": "WiFi状态"}, "flashlight": {"keywords": ["flashlight", "手电筒", "闪光灯"], "initial_method": "check_flashlight_status", "final_method": "check_flashlight_status", "description": "手电筒状态"}, "do_not_disturb": {"keywords": ["do not disturb", "dnd", "勿扰模式", "免打扰", "静音模式", "turn on do not disturb mode", "turn off do not disturb mode", "zen mode"], "initial_method": "check_do_not_disturb_status", "final_method": "check_do_not_disturb_status", "description": "Do Not Disturb状态"}, "mobile_data": {"keywords": ["Mobile data", "switched to data mode", "数据流量"], "initial_method": "check_mobile_data_status", "final_method": "check_mobile_data_status", "description": "Mobile data状态"}, "brightness": {"keywords": ["brightness", "decrease the brightness", "屏幕亮度"], "initial_method": "get_brightness_value", "final_method": "get_brightness_value", "description": "获取屏幕亮度数值"}, "location": {"keywords": ["turn on location services", "location"], "initial_method": "check_location_status", "final_method": "check_location_status", "description": "获取定位服务状态"}, "clock": {"keywords": ["clock", "alarm", "timer", "countdown 5 min", "时钟", "闹钟", "定时器"], "initial_method": "check_clock_app_opened", "final_method": "check_clock_app_opened", "description": "时钟应用状态"}, "facebook": {"keywords": ["facebook", "fb"], "initial_method": "check_facebook_app_opened", "final_method": "check_facebook_app_opened", "description": "Facebook应用状态"}, "settings": {"keywords": ["settings", "setting", "设置", "flash notification", "default mode", "通知", "flash notification", "闪光通知", "turn on do not disturb mode", "Switch to Barrage Notification", "set Battery Saver setting", "nfc"], "initial_method": "check_settings_opened", "final_method": "check_settings_opened", "description": "设置应用状态"}, "music": {"keywords": ["music", "音乐", "播放器"], "initial_method": "check_music_app_opened", "final_method": "check_music_app_opened", "description": "音乐应用状态"}, "gallery": {"keywords": ["gallery", "photos", "相册", "图片"], "initial_method": "check_gallery_app_opened", "final_method": "check_gallery_app_opened", "description": "相册应用状态"}, "calculator": {"keywords": ["calculator", "计算器"], "initial_method": "check_calculator_app_opened", "final_method": "check_calculator_app_opened", "description": "计算器应用状态"}, "google_maps": {"keywords": ["disneyland", "迪士尼", "地图", "google maps", "导航", "navigate", "navigation", "find a restaurant near me"], "initial_method": "check_google_map_app_opened", "final_method": "check_google_map_app_opened", "description": "google地图应用状态"}, "google_playstore": {"keywords": ["google playstore", "download"], "initial_method": "check_google_playstore_app_opened", "final_method": "check_google_playstore_app_opened", "description": "google playstore应用状态"}, "ScreenRecord": {"keywords": ["screen recording", "screen record", "start record"], "initial_method": "check_screen_record_app_opened", "final_method": "check_screen_record_app_opened", "description": "录屏软件用状态"}, "browser": {"keywords": ["browser", "chrome", "浏览器"], "initial_method": "check_browser_app_opened", "final_method": "check_browser_app_opened", "description": "浏览器应用状态"}, "carlcare": {"keywords": ["carlcare"], "initial_method": "check_carlcare_app_opened", "final_method": "check_carlcare_app_opened", "description": "carlcare应用状态"}, "healthlife": {"keywords": ["healthlife", "my health", "running", "walking"], "initial_method": "check_healthlife_app_opened", "final_method": "check_healthlife_app_opened", "description": "healthlife应用状态"}, "light_theme": {"keywords": ["light theme", "dark theme"], "initial_method": "check_light_theme_status", "final_method": "check_light_theme_status", "description": "深色/浅色模式状态"}, "Media_volume": {"keywords": ["maximum volume", "minimum volume", "increase the volume to the maximun", "decrease the volume to the minimun", "turn down the volume to the min"], "initial_method": "get_media_volume", "final_method": "get_media_volume", "description": "获取Media Volume系统音量，最大值15 最小值0"}, "youtube": {"keywords": ["youtube", "video", "play", "sotry", "视频播放器"], "initial_method": "check_youtube_app_opened", "final_method": "check_youtube_app_opened", "description": "youtube应用状态"}, "phonemaster": {"keywords": ["junk", "clean", "battery", "power", "memory"], "initial_method": "check_phonemaster_app_opened", "final_method": "check_phonemaster_app_opened", "description": "phonemaster应用状态"}, "whatsapp": {"keywords": ["whatsapp"], "initial_method": "check_whatsapp_app_opened", "final_method": "check_whatsapp_app_opened", "description": "whatsapp应用状态"}}}