"""
测试在AI图库打开图片状态下长按电源键调出Ella对话页面
"""
import pytest
import allure
import time
import subprocess
from testcases.test_ella.base_ella_test import BaseEllaTest
from pages.base.detectors.ai_gallery_detector import AiGalleryDetector
from pages.apps.ella.dialogue_page import EllaDialoguePage
from pages.base.app_detector import AppDetector, AppType
from core.logger import log
from core.base_driver import driver_manager


@allure.feature("Ella语音助手")
@allure.story("系统集成")
class TestPowerKeyInvokeEllaFromAiGallery(BaseEllaTest):
    """测试在AI图库打开图片状态下长按电源键调出Ella对话页面"""

    @allure.title("测试在AI图库打开图片状态下长按电源键调出Ella对话页面")
    @allure.description("在AI图库中打开一张图片，然后长按电源键1秒，验证是否能调出Ella对话页面")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_power_key_invoke_ella_from_ai_gallery(self):
        """测试在AI图库打开图片状态下长按电源键调出Ella对话页面"""

        # ai_gallery_detector = AiGalleryDetector()
        ella_page = EllaDialoguePage()

        try:
            # 清理环境
            self.clear_all_running_processes()

            # with allure.step("步骤1: 启动AI图库应用"):
            #     log.info("🚀 启动AI图库应用")
            #
            #     # 获取AI图库包名并尝试启动
            #     ai_gallery_packages = ai_gallery_detector.get_package_names()
            #     ai_gallery_started = False
            #     current_package = None
            #
            #     for package in ai_gallery_packages:
            #         try:
            #             log.info(f"尝试启动AI图库包: {package}")
            #             driver_manager.start_app(package)
            #             time.sleep(1)
            #
            #             # 验证应用是否启动成功
            #             if ai_gallery_detector.check_app_opened(AppType.AI_GALLERY):
            #                 log.info(f"✅ AI图库应用启动成功: {package}")
            #                 ai_gallery_started = True
            #                 current_package = package
            #                 break
            #         except Exception as e:
            #             log.warning(f"启动{package}失败: {e}")
            #             continue
            #
            #     assert ai_gallery_started, "AI图库应用启动失败"
            #
            #     # 截图记录AI图库启动状态
            #     self.take_screenshot_with_driver("ai_gallery_started")

            # with allure.step("步骤2: 在AI图库中打开一张图片"):
            #     log.info("📸 在AI图库中打开一张图片")
            #
            #     # 等待AI图库加载完成
            #     time.sleep(3)
            #
            #     # 尝试找到并点击第一张图片
            #     image_opened = self._open_first_image_in_gallery()
            #     assert image_opened, "无法在AI图库中打开图片"
            #
            #     # 等待图片完全加载
            #     time.sleep(2)
            #
            #     # 截图记录图片打开状态
            #     self.take_screenshot_with_driver("image_opened_in_gallery")

            with allure.step("步骤3: 长按电源键1秒"):
                log.info("🔋 长按电源键1秒")

                # 记录长按前的状态
                initial_ella_running = ella_page._check_app_started("com.transsion.aivoiceassistant")
                log.info(f"长按前Ella运行状态: {initial_ella_running}")

                # 长按电源键1秒
                self._long_press_power_key(duration=1.0)

                # 等待系统响应
                time.sleep(2)

                # 截图记录长按电源键后的状态
                self.take_screenshot_with_driver("after_power_key_long_press")

            # with allure.step("步骤4: 验证Ella对话页面是否调出"):
            #     log.info("🤖 验证Ella对话页面是否调出")
            #
            #     # 等待Ella可能的启动时间
            #     time.sleep(3)
            #
            #     # 检查Ella应用是否启动
            #     ella_app_started = ella_page._check_app_started("com.transsion.aivoiceassistant")
            #     log.info(f"Ella应用启动状态: {ella_app_started}")
            #
            #     # 检查是否在Ella对话页面
            #     ella_dialogue_visible = False
            #     if ella_app_started:
            #         # 等待页面加载
            #         ella_dialogue_visible = ella_page.wait_for_page_load(timeout=10)
            #         log.info(f"Ella对话页面可见: {ella_dialogue_visible}")
            #
            #     # 截图记录最终状态
            #     self.take_screenshot_with_driver("final_state")
            #
            #     # 验证结果
            #     assert ella_app_started, "长按电源键后Ella应用未启动"
            #     assert ella_dialogue_visible, "长按电源键后Ella对话页面未显示"
            #
            #     log.info("✅ 测试成功：长按电源键成功调出Ella对话页面")

#             with allure.step("记录测试结果"):
#                 summary = f"""
# 测试场景: 在AI图库打开图片状态下长按电源键调出Ella对话页面
# AI图库包名: {current_package}
# 长按前Ella状态: {initial_ella_running}
# 长按后Ella启动: {ella_app_started}
# Ella对话页面显示: {ella_dialogue_visible}
# 测试结果: 成功
# """
#                 self.attach_test_summary(summary.strip())

        except Exception as e:
            log.error(f"❌ 测试失败: {e}")
            # 失败时截图
            self.take_screenshot_with_driver("test_failed")
            raise

        finally:
            # 清理：停止相关应用
            try:
                log.info("🧹 清理测试环境")

                # 停止Ella应用
                if ella_page._check_app_started("com.transsion.aivoiceassistant"):
                    ella_page.stop_app()

                # 停止AI图库应用
                for package in ai_gallery_detector.get_package_names():
                    try:
                        driver_manager.stop_app(package)
                    except:
                        pass

            except Exception as e:
                log.warning(f"清理环境时出错: {e}")

    def _open_first_image_in_gallery(self) -> bool:
        """在AI图库中打开第一张图片"""
        try:
            log.info("尝试在AI图库中打开第一张图片")

            # 等待图库加载
            time.sleep(2)

            # 方法1: 尝试点击第一个图片缩略图
            # 查找图片视图元素
            image_views = driver_manager.driver(className="android.widget.ImageView")
            if image_views.exists(timeout=5):
                # 过滤出可点击的图片
                for i in range(min(image_views.count, 5)):  # 最多尝试前5个
                    try:
                        image_view = image_views[i] if image_views.count > 1 else image_views
                        info = image_view.info

                        # 检查是否是可点击的图片（通常有bounds且大小合理）
                        bounds = info.get('bounds', {})
                        if bounds and bounds.get('right', 0) - bounds.get('left', 0) > 50:
                            log.info(f"尝试点击第{i+1}个图片")
                            if image_view.click():
                                time.sleep(2)
                                log.info("✅ 成功点击图片")
                                return True
                    except Exception as e:
                        log.debug(f"点击第{i+1}个图片失败: {e}")
                        continue

            # 方法2: 尝试点击RecyclerView中的项目
            recycler_views = driver_manager.driver(className="androidx.recyclerview.widget.RecyclerView")
            if recycler_views.exists(timeout=3):
                try:
                    recycler_view = recycler_views[0] if recycler_views.count > 1 else recycler_views
                    # 点击RecyclerView的中心位置
                    bounds = recycler_view.info.get('bounds', {})
                    if bounds:
                        center_x = (bounds.get('left', 0) + bounds.get('right', 0)) // 2
                        center_y = (bounds.get('top', 0) + bounds.get('bottom', 0)) // 2
                        driver_manager.driver.click(center_x, center_y)
                        time.sleep(2)
                        log.info("✅ 通过RecyclerView点击成功")
                        return True
                except Exception as e:
                    log.debug(f"通过RecyclerView点击失败: {e}")

            # 方法3: 尝试点击屏幕中心区域（通常图片会在这里）
            log.info("尝试点击屏幕中心区域")
            screen_info = driver_manager.driver.info
            screen_width = screen_info.get('displayWidth', 1080)
            screen_height = screen_info.get('displayHeight', 1920)

            # 点击屏幕中心偏上的位置（通常图片网格在这里）
            center_x = screen_width // 2
            center_y = screen_height // 3

            driver_manager.driver.click(center_x, center_y)
            time.sleep(2)
            log.info("✅ 点击屏幕中心区域完成")
            return True

        except Exception as e:
            log.error(f"在AI图库中打开图片失败: {e}")
            return False

    def _long_press_power_key(self, duration: float = 1.0):
        """长按电源键"""
        try:
            log.info(f"长按电源键 {duration} 秒")

            # 方法1: 使用adb shell命令长按电源键
            # 注意：不同设备的电源键键码可能不同，常见的有KEYCODE_POWER (26)

            # 开始按下电源键
            cmd_down = ["adb", "shell", "sendevent", "/dev/input/event0", "1", "116", "1"]
            cmd_sync1 = ["adb", "shell", "sendevent", "/dev/input/event0", "0", "0", "0"]

            # 松开电源键
            cmd_up = ["adb", "shell", "sendevent", "/dev/input/event0", "1", "116", "0"]
            cmd_sync2 = ["adb", "shell", "sendevent", "/dev/input/event0", "0", "0", "0"]

            try:
                # 按下
                subprocess.run(cmd_down, timeout=5, capture_output=True)
                subprocess.run(cmd_sync1, timeout=5, capture_output=True)

                # 保持按下状态
                time.sleep(duration)

                # 松开
                subprocess.run(cmd_up, timeout=5, capture_output=True)
                subprocess.run(cmd_sync2, timeout=5, capture_output=True)

                log.info("✅ 使用sendevent长按电源键成功")
                return

            except Exception as e:
                log.warning(f"sendevent方法失败: {e}")

            # 方法2: 备选方案 - 使用input keyevent（但这通常是短按）
            try:
                # 这种方法通常只能短按，但我们可以尝试
                cmd = ["adb", "shell", "input", "keyevent", "KEYCODE_POWER"]
                subprocess.run(cmd, timeout=5, capture_output=True)
                log.info("✅ 使用keyevent按电源键（短按）")

            except Exception as e:
                log.warning(f"keyevent方法也失败: {e}")

        except Exception as e:
            log.error(f"长按电源键失败: {e}")
            raise

    def take_screenshot_with_driver(self, filename: str):
        """使用driver_manager截图"""
        try:
            screenshot_path = driver_manager.screenshot(f"{filename}.png")
            log.info(f"截图保存: {screenshot_path}")

            # 添加到Allure报告
            with open(screenshot_path, 'rb') as f:
                allure.attach(f.read(), name=filename, attachment_type=allure.attachment_type.PNG)

        except Exception as e:
            log.warning(f"截图失败: {e}")
