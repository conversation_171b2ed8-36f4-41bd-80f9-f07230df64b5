"""
Ella语音助手基础指令
"""
import pytest
import allure
from testcases.test_ella.base_ella_test1 import SimpleEllaTest1


@allure.feature("Ella语音助手")
@allure.story("打开应用")
class TestEllaSuspended(SimpleEllaTest1):
    """Ella打开whatsapp测试类"""

    @allure.title("测试长按电源键出现ella悬浮")
    @allure.description("测试长按电源键出现ella悬浮")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_open_whatsapp(self, ella_app):
        """测试长按电源键出现ella悬浮"""
        command = "search the address in the image"
        app_name = 'googlemap'

        with allure.step(f"执行命令: {command}"):
            initial_status, final_status, response_text, files_status = self.simple_command_test1(
                ella_app, command
            )

        # with allure.step("验证响应包含期望内容"):
        #     expected_text = ['Done']
        #     result = self.verify_expected_in_response(expected_text, response_text)
        #     assert result, f"响应文本应包含{expected_text}，实际响应: '{response_text}'"

        with allure.step(f"验证{app_name}已打开"):
            assert final_status, f"{app_name}: 初始={initial_status}, 最终={final_status}, 响应='{response_text}'"

        with allure.step("记录测试结果"):
            summary = self.create_test_summary(command, initial_status, final_status, response_text)
            self.attach_test_summary(summary)
            self.take_screenshot(ella_app, "test_completed")
