#!/usr/bin/env python3
"""
专门测试Ella悬浮窗检测的脚本
基于用户提供的信息：长按电源键后焦点窗口变为 aivoiceassistant_Window
"""
import sys
import os
import time
import subprocess

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from core.logger import log


def check_current_focus():
    """检查当前焦点窗口"""
    try:
        log.info("🔍 检查当前焦点窗口...")
        
        # 方法1: 使用findstr (Windows)
        try:
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "window", "|", "findstr", "mCurrentFocus"],
                capture_output=True, text=True, timeout=5, shell=True
            )
            
            if result.returncode == 0:
                focus_output = result.stdout.strip()
                log.info(f"焦点窗口 (findstr): {focus_output}")
                
                if "aivoiceassistant_Window" in focus_output:
                    log.info("✅ 检测到Ella悬浮窗！")
                    return True
                else:
                    log.info("❌ 当前焦点不是Ella悬浮窗")
                    
        except Exception as e:
            log.warning(f"findstr方法失败: {e}")
        
        # 方法2: 使用grep (Linux/Mac)
        try:
            result = subprocess.run(
                ["adb", "shell", "dumpsys window | grep mCurrentFocus"],
                capture_output=True, text=True, timeout=5, shell=True
            )
            
            if result.returncode == 0:
                focus_output = result.stdout.strip()
                log.info(f"焦点窗口 (grep): {focus_output}")
                
                if "aivoiceassistant_Window" in focus_output:
                    log.info("✅ 检测到Ella悬浮窗！")
                    return True
                    
        except Exception as e:
            log.warning(f"grep方法失败: {e}")
        
        # 方法3: 直接解析dumpsys输出
        try:
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "window"],
                capture_output=True, text=True, timeout=10
            )
            
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                for line in lines:
                    if "mCurrentFocus=" in line:
                        log.info(f"焦点窗口 (直接解析): {line.strip()}")
                        
                        if "aivoiceassistant_Window" in line:
                            log.info("✅ 检测到Ella悬浮窗！")
                            return True
                        break
                        
        except Exception as e:
            log.warning(f"直接解析方法失败: {e}")
        
        return False
        
    except Exception as e:
        log.error(f"检查焦点窗口失败: {e}")
        return False


def check_all_windows():
    """检查所有窗口信息"""
    try:
        log.info("🔍 检查所有窗口信息...")
        
        result = subprocess.run(
            ["adb", "shell", "dumpsys", "window", "windows"],
            capture_output=True, text=True, timeout=15
        )
        
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            ella_related_lines = []
            
            for line in lines:
                if "aivoiceassistant" in line.lower():
                    ella_related_lines.append(line.strip())
            
            if ella_related_lines:
                log.info(f"✅ 找到{len(ella_related_lines)}个Ella相关窗口:")
                for i, line in enumerate(ella_related_lines[:10]):  # 最多显示10个
                    log.info(f"  {i+1}. {line}")
                return True
            else:
                log.info("❌ 未找到Ella相关窗口")
                return False
                
    except Exception as e:
        log.error(f"检查所有窗口失败: {e}")
        return False


def monitor_focus_changes(duration=30):
    """监控焦点窗口变化"""
    try:
        log.info(f"🔍 监控焦点窗口变化 ({duration}秒)...")
        log.info("请在监控期间长按电源键调出Ella悬浮窗")
        
        start_time = time.time()
        last_focus = ""
        
        while time.time() - start_time < duration:
            try:
                result = subprocess.run(
                    ["adb", "shell", "dumpsys", "window", "|", "findstr", "mCurrentFocus"],
                    capture_output=True, text=True, timeout=3, shell=True
                )
                
                if result.returncode == 0:
                    current_focus = result.stdout.strip()
                    
                    if current_focus != last_focus:
                        log.info(f"焦点变化: {current_focus}")
                        last_focus = current_focus
                        
                        if "aivoiceassistant_Window" in current_focus:
                            log.info("🎉 检测到Ella悬浮窗出现！")
                            return True
                
                time.sleep(1)  # 每秒检查一次
                
            except Exception as e:
                log.debug(f"监控过程中出错: {e}")
                time.sleep(1)
                continue
        
        log.info("监控结束，未检测到Ella悬浮窗")
        return False
        
    except Exception as e:
        log.error(f"监控焦点变化失败: {e}")
        return False


def test_adb_connection():
    """测试adb连接"""
    try:
        log.info("🔍 测试adb连接...")
        
        result = subprocess.run(
            ["adb", "devices"],
            capture_output=True, text=True, timeout=5
        )
        
        if result.returncode == 0:
            devices = result.stdout.strip()
            log.info(f"ADB设备列表:\n{devices}")
            
            lines = devices.split('\n')[1:]  # 跳过标题行
            connected_devices = [line for line in lines if line.strip() and 'device' in line]
            
            if connected_devices:
                log.info(f"✅ 找到{len(connected_devices)}个连接的设备")
                return True
            else:
                log.error("❌ 没有找到连接的设备")
                return False
        else:
            log.error("❌ adb命令执行失败")
            return False
            
    except Exception as e:
        log.error(f"测试adb连接失败: {e}")
        return False


def main():
    """主函数"""
    log.info("🎯 Ella悬浮窗检测测试")
    log.info("=" * 60)
    
    # 测试adb连接
    if not test_adb_connection():
        log.error("❌ adb连接测试失败，请检查设备连接")
        return 1
    
    # 检查当前焦点
    log.info("\n" + "=" * 40)
    current_focus_detected = check_current_focus()
    
    # 检查所有窗口
    log.info("\n" + "=" * 40)
    all_windows_detected = check_all_windows()
    
    # 提供监控选项
    log.info("\n" + "=" * 40)
    user_input = input("是否要监控焦点窗口变化？请在30秒内长按电源键 (y/n): ")
    
    if user_input.lower() in ['y', 'yes']:
        monitor_detected = monitor_focus_changes(30)
    else:
        monitor_detected = False
    
    # 总结结果
    log.info("\n" + "=" * 60)
    log.info("🎯 检测结果总结:")
    log.info(f"当前焦点检测: {'✅ 成功' if current_focus_detected else '❌ 未检测到'}")
    log.info(f"所有窗口检测: {'✅ 成功' if all_windows_detected else '❌ 未检测到'}")
    log.info(f"监控检测: {'✅ 成功' if monitor_detected else '❌ 未检测到或未执行'}")
    
    if current_focus_detected or all_windows_detected or monitor_detected:
        log.info("🎉 至少一种方法检测到了Ella相关窗口")
        return 0
    else:
        log.info("❌ 所有方法都未检测到Ella悬浮窗")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
