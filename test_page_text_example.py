#!/usr/bin/env python3
"""
页面文本抓取功能的测试用例示例
展示如何在Ella测试中使用页面文本抓取功能
"""
import pytest
import allure
import time
from testcases.test_ella.base_ella_test import BaseEllaTest
from core.logger import log


@allure.feature("页面文本抓取")
@allure.story("功能验证")
class TestPageTextExtraction(BaseEllaTest):
    """页面文本抓取功能测试"""

    @allure.title("测试打开设置应用并抓取页面文本")
    @allure.description("通过Ella语音命令打开设置应用，然后抓取设置页面的所有文本内容")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.smoke
    def test_open_settings_and_extract_text(self, ella_app):
        """测试打开设置应用并抓取页面文本"""
        
        command = "open settings"
        
        try:
            with allure.step(f"执行命令: {command}"):
                # 执行命令并获取响应
                initial_status, final_status, response_text, _ = self.execute_command_and_verify(
                    ella_app, command, expected_status_change=True
                )
                
                log.info(f"命令执行完成，响应: {response_text}")
            
            with allure.step("等待设置应用完全加载"):
                time.sleep(3)  # 等待设置应用完全加载
            
            with allure.step("抓取设置页面文本内容"):
                # 抓取当前页面（设置应用）的文本
                page_text_data = self._get_page_text(ella_app)
                
                # 验证抓取是否成功
                assert page_text_data['success'], "页面文本抓取失败"
                assert page_text_data['text_count'] > 0, "未抓取到任何文本内容"
                
                log.info(f"✅ 成功抓取到 {page_text_data['text_count']} 个文本元素")
                
                # 验证是否在设置应用中
                app_info = page_text_data['current_app']
                assert 'settings' in app_info['package'].lower(), f"当前不在设置应用中: {app_info['package']}"
                
                log.info(f"✅ 确认在设置应用中: {app_info['package']}")
            
            with allure.step("分析页面文本内容"):
                # 检查是否包含设置相关的文本
                all_texts = [item['text'].lower() for item in page_text_data['all_texts']]
                combined_text = " ".join(all_texts)
                
                # 设置应用中常见的文本
                expected_keywords = ['设置', 'settings', '网络', 'network', '显示', 'display', '声音', 'sound']
                found_keywords = []
                
                for keyword in expected_keywords:
                    if keyword in combined_text:
                        found_keywords.append(keyword)
                
                log.info(f"找到的设置相关关键词: {found_keywords}")
                assert len(found_keywords) > 0, f"未找到设置相关关键词，页面文本: {combined_text[:200]}..."
            
            with allure.step("保存页面文本数据"):
                # 保存页面文本到文件
                filename = f"settings_page_text_{int(time.time())}.json"
                file_path = self.save_page_text_to_file(page_text_data, filename)
                
                if file_path:
                    log.info(f"✅ 页面文本已保存到: {file_path}")
                    
                    # 附加到Allure报告
                    with open(file_path, 'r', encoding='utf-8') as f:
                        allure.attach(f.read(), name="设置页面文本数据", attachment_type=allure.attachment_type.JSON)
                
                # 生成并附加页面摘要
                summary = self.get_page_text_summary(page_text_data)
                allure.attach(summary, name="设置页面文本摘要", attachment_type=allure.attachment_type.TEXT)
                log.info(f"页面文本摘要:\n{summary}")
            
            with allure.step("记录测试结果"):
                test_summary = f"""
测试命令: {command}
AI响应: {response_text}
目标应用: {app_info['package']}
抓取文本数量: {page_text_data['text_count']}
找到关键词: {found_keywords}
测试结果: 成功
"""
                self.attach_test_summary(test_summary.strip())
                
                # 截图
                self.take_screenshot(ella_app, "settings_page_with_text")
            
            log.info("✅ 设置应用页面文本抓取测试完成")
            
        except Exception as e:
            log.error(f"❌ 测试失败: {e}")
            # 失败时也尝试抓取页面文本用于调试
            try:
                debug_text_data = self._get_page_text(ella_app)
                if debug_text_data['success']:
                    debug_summary = self.get_page_text_summary(debug_text_data)
                    allure.attach(debug_summary, name="失败时页面文本", attachment_type=allure.attachment_type.TEXT)
            except:
                pass
            raise

    @allure.title("测试打开联系人应用并验证页面文本")
    @allure.description("通过Ella语音命令打开联系人应用，验证页面文本是否包含联系人相关内容")
    @allure.severity(allure.severity_level.NORMAL)
    def test_open_contacts_and_verify_text(self, ella_app):
        """测试打开联系人应用并验证页面文本"""
        
        command = "open contacts"
        
        try:
            with allure.step(f"执行命令: {command}"):
                # 执行命令
                initial_status, final_status, response_text, _ = self.execute_command_and_verify(
                    ella_app, command, expected_status_change=True
                )
            
            with allure.step("等待联系人应用加载并抓取文本"):
                time.sleep(3)
                
                # 抓取页面文本
                page_text_data = self._get_page_text(ella_app)
                assert page_text_data['success'], "页面文本抓取失败"
                
                log.info(f"抓取到 {page_text_data['text_count']} 个文本元素")
            
            with allure.step("验证联系人应用特征文本"):
                # 检查联系人应用特征
                all_texts = [item['text'].lower() for item in page_text_data['all_texts']]
                combined_text = " ".join(all_texts)
                
                # 联系人应用可能包含的文本
                contact_keywords = ['联系人', 'contacts', '电话', 'phone', '添加', 'add', '搜索', 'search']
                found_keywords = [kw for kw in contact_keywords if kw in combined_text]
                
                log.info(f"找到的联系人相关关键词: {found_keywords}")
                
                # 验证应用包名
                app_info = page_text_data['current_app']
                is_contacts_app = 'contact' in app_info['package'].lower() or 'dialer' in app_info['package'].lower()
                
                # 至少要满足一个条件：包名正确或找到相关关键词
                assert is_contacts_app or len(found_keywords) > 0, \
                    f"未确认在联系人应用中。包名: {app_info['package']}, 关键词: {found_keywords}"
            
            with allure.step("生成测试报告"):
                # 保存文本数据
                filename = f"contacts_page_text_{int(time.time())}.json"
                self.save_page_text_to_file(page_text_data, filename)
                
                # 生成摘要
                summary = self.get_page_text_summary(page_text_data)
                allure.attach(summary, name="联系人页面文本摘要", attachment_type=allure.attachment_type.TEXT)
                
                # 测试总结
                test_summary = f"""
测试命令: {command}
AI响应: {response_text}
目标应用: {app_info['package']}
抓取文本数量: {page_text_data['text_count']}
找到关键词: {found_keywords}
测试结果: 成功
"""
                self.attach_test_summary(test_summary.strip())
            
            log.info("✅ 联系人应用页面文本验证测试完成")
            
        except Exception as e:
            log.error(f"❌ 测试失败: {e}")
            raise

    @allure.title("测试页面文本抓取的性能")
    @allure.description("测试页面文本抓取功能的性能表现")
    @allure.severity(allure.severity_level.MINOR)
    def test_page_text_extraction_performance(self, ella_app):
        """测试页面文本抓取的性能"""
        
        try:
            with allure.step("多次抓取页面文本测试性能"):
                extraction_times = []
                text_counts = []
                
                # 进行5次抓取测试
                for i in range(5):
                    start_time = time.time()
                    
                    page_text_data = self._get_page_text(ella_app)
                    
                    end_time = time.time()
                    extraction_time = end_time - start_time
                    
                    extraction_times.append(extraction_time)
                    text_counts.append(page_text_data['text_count'] if page_text_data['success'] else 0)
                    
                    log.info(f"第{i+1}次抓取: {extraction_time:.2f}秒, {text_counts[-1]}个文本元素")
                    
                    time.sleep(1)  # 间隔1秒
            
            with allure.step("分析性能数据"):
                avg_time = sum(extraction_times) / len(extraction_times)
                max_time = max(extraction_times)
                min_time = min(extraction_times)
                avg_count = sum(text_counts) / len(text_counts)
                
                performance_report = f"""
性能测试结果:
- 平均抓取时间: {avg_time:.2f}秒
- 最长抓取时间: {max_time:.2f}秒  
- 最短抓取时间: {min_time:.2f}秒
- 平均文本数量: {avg_count:.1f}个
- 测试次数: {len(extraction_times)}次
"""
                
                log.info(performance_report)
                allure.attach(performance_report, name="性能测试报告", attachment_type=allure.attachment_type.TEXT)
                
                # 性能断言
                assert avg_time < 10.0, f"平均抓取时间过长: {avg_time:.2f}秒"
                assert max_time < 15.0, f"最长抓取时间过长: {max_time:.2f}秒"
                assert avg_count > 0, "平均文本数量为0"
            
            log.info("✅ 页面文本抓取性能测试完成")
            
        except Exception as e:
            log.error(f"❌ 性能测试失败: {e}")
            raise


if __name__ == "__main__":
    # 可以直接运行单个测试
    pytest.main([__file__, "-v", "--alluredir=reports/allure-results"])
