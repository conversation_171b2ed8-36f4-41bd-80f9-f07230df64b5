# AI图库长按电源键调出Ella测试用例

## 概述

这个测试用例用于验证在AI图库应用中打开图片的状态下，长按电源键1秒是否能成功调出Ella对话页面。

## 测试文件

- **主测试文件**: `testcases/test_ella/system_coupling/test_power_key_invoke_ella_from_ai_gallery.py`
- **演示脚本**: `test_power_key_ella_demo.py`
- **说明文档**: `README_power_key_test.md`

## 测试场景

1. **启动AI图库应用**
   - 尝试启动多个可能的AI图库包名
   - 验证应用是否成功启动

2. **在AI图库中打开图片**
   - 查找并点击第一张可用图片
   - 等待图片完全加载

3. **长按电源键1秒**
   - 使用adb命令模拟长按电源键
   - 等待系统响应

4. **验证Ella对话页面是否调出**
   - 检查Ella应用是否启动
   - 验证Ella对话页面是否可见

## 使用方法

### 运行完整测试

```bash
# 使用pytest运行测试
pytest testcases/test_ella/system_coupling/test_power_key_invoke_ella_from_ai_gallery.py -v

# 生成Allure报告
pytest testcases/test_ella/system_coupling/test_power_key_invoke_ella_from_ai_gallery.py --alluredir=reports/allure-results
allure serve reports/allure-results
```

### 运行演示脚本

```bash
# 运行演示脚本（安全模式，不会实际按电源键）
python test_power_key_ella_demo.py
```

## 技术实现

### 关键组件

1. **AiGalleryDetector**: 检测和启动AI图库应用
2. **EllaDialoguePage**: 管理Ella对话页面的交互
3. **BaseEllaTest**: 提供测试基础功能和工具方法

### 长按电源键实现

测试用例使用两种方法模拟长按电源键：

1. **sendevent方法**（主要方法）:
   ```bash
   adb shell sendevent /dev/input/event0 1 116 1  # 按下
   adb shell sendevent /dev/input/event0 0 0 0    # 同步
   # 等待1秒
   adb shell sendevent /dev/input/event0 1 116 0  # 松开
   adb shell sendevent /dev/input/event0 0 0 0    # 同步
   ```

2. **keyevent方法**（备选方案）:
   ```bash
   adb shell input keyevent KEYCODE_POWER
   ```

### AI图库图片打开策略

测试用例使用多种策略来打开图片：

1. **ImageView点击**: 查找可点击的ImageView元素
2. **RecyclerView点击**: 点击RecyclerView中心位置
3. **屏幕坐标点击**: 点击屏幕中心偏上位置

## 配置要求

### 设备要求

- Android设备已连接并通过adb可访问
- 设备已安装AI图库应用
- 设备支持长按电源键调出Ella功能

### 软件要求

- Python 3.7+
- pytest
- allure-pytest
- uiautomator2
- 项目依赖的其他包

### AI图库应用包名

测试支持以下AI图库应用包名：
- `com.gallery20`
- `com.transsion.aigallery`
- `com.android.aigallery`
- `com.sh.smart.aigallery`

## 测试结果

### 成功标准

- AI图库应用成功启动
- 成功在图库中打开一张图片
- 长按电源键后Ella应用启动
- Ella对话页面正确显示

### 失败处理

- 自动截图记录失败状态
- 详细的错误日志
- 自动清理测试环境

## 注意事项

### 安全考虑

- 长按电源键可能触发设备的电源菜单或其他系统功能
- 在某些设备上可能需要调整电源键的键码
- 建议在测试设备上进行，避免影响生产环境

### 设备兼容性

- 不同设备的电源键键码可能不同
- 某些设备可能不支持sendevent方法
- UI元素的定位可能因设备和应用版本而异

### 调试建议

1. **检查设备连接**:
   ```bash
   adb devices
   ```

2. **检查AI图库应用**:
   ```bash
   adb shell pm list packages | grep gallery
   ```

3. **检查Ella应用**:
   ```bash
   adb shell pm list packages | grep ella
   ```

4. **查看设备输入设备**:
   ```bash
   adb shell getevent
   ```

## 扩展功能

### 自定义配置

可以通过修改测试类的属性来自定义测试行为：

```python
class TestPowerKeyInvokeEllaFromAiGallery(BaseEllaTest):
    # 自定义长按时长
    POWER_KEY_DURATION = 1.5
    
    # 自定义等待时间
    WAIT_TIME_AFTER_POWER_KEY = 5
```

### 添加更多验证

可以扩展测试用例以包含更多验证：

- 验证Ella的具体响应
- 检查系统状态变化
- 验证其他应用的状态

## 故障排除

### 常见问题

1. **AI图库启动失败**
   - 检查设备是否安装了AI图库应用
   - 确认包名是否正确

2. **长按电源键无效**
   - 尝试不同的键码值
   - 检查设备的输入设备路径

3. **Ella未启动**
   - 确认设备支持长按电源键调出Ella功能
   - 检查Ella应用是否已安装

### 日志分析

测试过程中会生成详细的日志，包括：
- 应用启动状态
- UI元素查找过程
- 电源键操作结果
- Ella启动验证

## 贡献

如果需要改进测试用例，请考虑以下方面：
- 增加更多设备兼容性
- 优化图片查找策略
- 改进错误处理机制
- 添加更多验证点
